﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Mappings;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Data;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task UnitHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkUnitUploadTracker? tracker = (await _bulkUnitUploadRepo.ListAsync(new GetBulkUnitByTrackerIdSpec(input.TrackerId))).FirstOrDefault();
            try
            {
                if (tracker != null)
                {
                    try
                    {
                        tracker.MappedColumnData = tracker.MappedColumnData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        tracker.Status = Lrb.Domain.Enums.UploadStatus.Started;
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;

                        await _bulkUnitUploadRepo.UpdateAsync(tracker);
                        Console.WriteLine($"handler() -> UnitHandler Updated Status: {tracker.Status} \n {JsonConvert.SerializeObject(tracker)}");

                        var unitTypes = new List<MasterAreaUnit>(await _masterAreaUnitRepo.ListAsync(cancellationToken));
                        var projectTypes = new List<MasterProjectType>(await _projectTypeRepo.ListAsync(cancellationToken));
                        var unitTypesAttributes = new List<CustomMasterAttribute>(await _masterProjectUnitAttributeRepo.ListAsync(cancellationToken));
                        var globalSettingInfo = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);

                        #region Convert To DataTable
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", tracker.S3BucketKey);
                        DataTable dataTable = new();
                        if (tracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                        }
                        else
                        {
                            dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, tracker.SheetName);
                        }
                        
                        int totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            var row = dataTable.Rows[i];
                            //string name = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.Name, out var nameColumn))
                            //{
                            //    name = row[nameColumn]?.ToString() ?? string.Empty;
                            //}
                            //string unit = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.UnitArea, out var unitColumn))
                            //{
                            //    unit = row[unitColumn]?.ToString() ?? string.Empty;
                            //}
                            //string price = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.PricePerUnit, out var priceColumn))
                            //{
                            //    price = row[priceColumn]?.ToString() ?? string.Empty;
                            //}
                            //string totalPrice = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.TotalPrice, out var totalPriceColumn))
                            //{
                            //    totalPrice = row[totalPriceColumn]?.ToString() ?? string.Empty;
                            //}
                            //string unitType = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.UnitType, out var unitTypeColumn))
                            //{
                            //    unitType = row[unitTypeColumn]?.ToString() ?? string.Empty;
                            //}
                            //string unitSubType = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.UnitSubType, out var unitSubTypeColumn))
                            //{
                            //    unitSubType = row[unitSubTypeColumn]?.ToString() ?? string.Empty;
                            //}
                            //string areaUnit = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.AreaUnit, out var areaUnitColumn))
                            //{
                            //    areaUnit = row[areaUnitColumn]?.ToString() ?? string.Empty;
                            //}
                            //string bhk = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.NoOfBHK, out var bhkColumn))
                            //{
                            //    bhk = row[bhkColumn]?.ToString() ?? string.Empty;
                            //}
                            //string bhkType = string.Empty;
                            //if (tracker.MappedColumnData.TryGetValue(UnitDataColumn.BHKType, out var bhkTypeColumn))
                            //{
                            //    bhkType = row[bhkTypeColumn]?.ToString() ?? string.Empty;
                            //}

                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i?.ToString())))
                            {
                                row.Delete();
                            }
                            //else if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(unit))
                            //{
                            //    var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                            //    var invalidData = new InvalidUnitDto
                            //    {
                            //        Errors = "Name and UnitArea are empty",
                            //        Notes = notes
                            //    };
                            //    if (!invalids.Any(i => i.Notes == invalidData.Notes))
                            //    {
                            //        invalids.Add(invalidData);
                            //    }
                            //    else
                            //    {
                            //        var index = invalids.FindIndex(i => i.Notes == invalidData.Notes);
                            //        if (index != -1)
                            //        {
                            //            invalids[index].RepeatedCount++;
                            //        }
                            //    }
                            //    row.Delete();
                            //}
                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        totalRows = dataTable.Rows.Count;
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        #endregion
                        //var project = await _newProjectRepo.GetByIdAsync(tracker.ProjectIds?.FirstOrDefault() ?? Guid.Empty);
                        var project = await _newProjectRepo.FirstOrDefaultAsync(new ProjectByIdSpec(tracker.ProjectIds?.FirstOrDefault() ?? Guid.Empty));
                        var unMappedColumn = dataTable.GetUnmappedUnitColumnNames(tracker?.MappedColumnData);
                        var units = dataTable.ConvertToUnit(tracker.MappedColumnData, unitTypes, projectTypes, unitTypesAttributes, globalSettingInfo, project);

                        units.ForEach(unit =>
                        {
                            unit.SetUnit(input.CurrentUserId);
                            if (project != null)
                            {
                                unit.Project = project;
                            }
                        });

                        var invalidData = Lrb.Application.Project.Web.Mappings.UnitHelper.GetInvalidUnits(units);

                        tracker.Status = UploadStatus.InProgress;
                        tracker.TotalCount = totalRows;
                        tracker.DistinctUnitCount = 0;
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        tracker.InvalidCount = invalidData.Count();
                        if (invalidData.Any())
                        {
                            tracker.DuplicateCount = 0;
                            byte[] bytes = ExcelHelper.CreateExcelFromList(invalidData).ToArray();
                            string fileName = $"InvalidUnit-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = "units";
                            var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            tracker.InvalidDataS3BucketKey = key;
                        }
                        await _bulkUnitUploadRepo.UpdateAsync(tracker);
                        Console.WriteLine($"handler() -> BulkUnitUploadTracker Updated Status: {tracker.Status} \n {JsonConvert.SerializeObject(tracker)}");
                        BulkUploadBackgroundDto backgroundDto = new();
                        if (units.Count > 0)
                        {
                            int unitsPerchunk = units.Count > 5000 ? 5000 : units.Count;
                            var chunks = units.Chunk(unitsPerchunk).Select(i => new ConcurrentBag<UnitType>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkUploadBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = tracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Units = new(chunk),
                                    UserIds = new(tracker.UserIds ?? new())
                                };
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }
                        tracker.Status = UploadStatus.Completed;
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _bulkUnitUploadRepo.UpdateAsync(tracker);
                    }
                    catch (Exception ex)
                    {
                        tracker = await _bulkUnitUploadRepo.GetByIdAsync(tracker.Id);
                        tracker.Status = UploadStatus.Failed;
                        tracker.Message = ex.Message;
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _bulkUnitUploadRepo.UpdateAsync(tracker);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkUnitUploadTrackerUsingEPPlus -> UnitHandler()"
                        };
                        throw;

                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkUnitUploadTrackerUsingEPPlus -> UnitHandler()"
                };
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;

            }
        }
        public async Task ExecuteDBOperationsAsync(BulkUploadBackgroundDto dto)
        {
            BulkChannelPartnerUploadTracker bulkChannelPartner = new();
            var tracker = (await _bulkUnitUploadRepo.ListAsync(new GetBulkUnitByTrackerIdSpec(dto.TrackerId))).FirstOrDefault();
            try
            {
                await _unitRepo.AddRangeAsync(dto.Units);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Units.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkUnitUploadRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkUnitUploadRepo.UpdateAsync(tracker);
            }
        }
    }
}
