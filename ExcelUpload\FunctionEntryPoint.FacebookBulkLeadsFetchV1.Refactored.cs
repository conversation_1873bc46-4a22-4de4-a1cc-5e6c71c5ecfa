using Lrb.Application.Agency;
using Lrb.Application.Agency.Web;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.Integration.Web.Automation;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Mapster;
using MessagePack;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using System.Threading;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : FBCommonHandler, IFunctionEntryPoint
    {
        public async Task FacebookBulkLeadsFetchHandlerV1(InputPayload input)
        {
            var tenantId = input.TenantId;
            var userId = input.CurrentUserId;
            var trackerId = input.TrackerId;
            
            if (string.IsNullOrWhiteSpace(tenantId))
                return;

            try
            {
                var processingContext = await InitializeProcessingContextAsync(trackerId, userId, input.JsonData);
                if (processingContext == null)
                    return;

                await UpdateTrackerStatusAsync(processingContext.Tracker, UploadStatus.Started, userId);

                var subscribedAdsAndForms = await _facebookService.GetAdsAndFormsWithUserOrPageAccessTokenAsync(tenantId);
                if (!subscribedAdsAndForms?.Any() ?? true)
                {
                    await UpdateTrackerStatusAsync(processingContext.Tracker, UploadStatus.Completed, userId);
                    return;
                }

                await UpdateTrackerWithCountsAsync(processingContext.Tracker, subscribedAdsAndForms, userId);

                var processedLeads = await ProcessFacebookLeadsAsync(subscribedAdsAndForms, processingContext);
                
                if (processedLeads.ValidLeads.Any())
                {
                    await SaveLeadsAndUpdateEntitiesAsync(processedLeads, processingContext);
                }

                await FinalizeProcessingAsync(processingContext.Tracker, processedLeads, userId);
            }
            catch (Exception ex)
            {
                await HandleProcessingErrorAsync(trackerId, userId, ex);
            }
        }

        private async Task<ProcessingContext?> InitializeProcessingContextAsync(Guid trackerId, Guid userId, string? jsonData)
        {
            var globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec());
            var tracker = await _fbBulkLeadFetchTrackerRepository.GetByIdAsync(trackerId);
            
            if (tracker == null)
                return null;

            var timeZone = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData ?? string.Empty) ?? new();
            var fromDate = tracker.FromDate != null 
                ? ToParticularTimeZone(tracker.FromDate ?? DateTime.UtcNow.Date.ConvertFromDateToUtc(), timeZone.TimeZoneId, timeZone.BaseUTcOffset)
                : tracker.FromDate;
            var toDate = tracker.ToDate != null 
                ? ToParticularTimeZone(tracker.ToDate ?? DateTime.UtcNow, timeZone.TimeZoneId, timeZone.BaseUTcOffset)
                : tracker.ToDate;

            var customStatus = await _customMastereadStatus.FirstOrDefaultAsync(new GetDefaultStatusSpec());
            var defaultStatus = await _customMastereadStatus.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string> { "new" }), CancellationToken.None);

            return new ProcessingContext
            {
                Tracker = tracker,
                GlobalSettings = globalSettings,
                FromDate = fromDate,
                ToDate = toDate,
                CustomStatus = customStatus,
                DefaultStatus = defaultStatus,
                UserId = userId
            };
        }

        private async Task UpdateTrackerStatusAsync(FbBulkLeadFetchTracker tracker, UploadStatus status, Guid userId)
        {
            tracker.Status = status;
            tracker.LastModifiedBy = userId;
            await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
            Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker Status Updated: {status}");
        }

        private async Task UpdateTrackerWithCountsAsync(FbBulkLeadFetchTracker tracker, List<dynamic> subscribedAdsAndForms, Guid userId)
        {
            tracker.ActiveAdsCount = subscribedAdsAndForms?.Count(i => i?.Type?.Contains("ad", StringComparison.InvariantCultureIgnoreCase) ?? false) ?? 0;
            tracker.ActiveFormsCount = subscribedAdsAndForms?.Count(i => i?.Type?.Contains("form", StringComparison.InvariantCultureIgnoreCase) ?? false) ?? 0;
            tracker.Status = UploadStatus.InProgress;
            tracker.LastModifiedBy = userId;
            await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
        }

        private async Task<ProcessedLeadsResult> ProcessFacebookLeadsAsync(List<dynamic> subscribedAdsAndForms, ProcessingContext context)
        {
            // Pre-load all required data in bulk to minimize database calls
            var dataCache = await LoadRequiredDataAsync(subscribedAdsAndForms);
            
            // Fetch Facebook leads in bulk
            var fbLeadsCache = await FetchFacebookLeadsAsync(subscribedAdsAndForms, context.FromDate, context.ToDate);
            
            var allLeads = new List<Lead>();
            var ads = new List<FacebookAdsInfo>();
            var forms = new List<FacebookLeadGenForm>();
            var integrationAccounts = new List<IntegrationAccountInfo>();
            int fetchedLeadsCount = 0;

            // Process each ad/form
            foreach (var adOrForm in subscribedAdsAndForms.OrderBy(x => x.Type))
            {
                var leadBatch = await ProcessSingleAdOrFormAsync(adOrForm, dataCache, fbLeadsCache, context);
                
                allLeads.AddRange(leadBatch.Leads);
                fetchedLeadsCount += leadBatch.FetchedCount;
                
                if (leadBatch.Ad != null) ads.Add(leadBatch.Ad);
                if (leadBatch.Form != null) forms.Add(leadBatch.Form);
                if (leadBatch.IntegrationAccount != null) integrationAccounts.Add(leadBatch.IntegrationAccount);
            }

            // Remove duplicates and validate leads
            var validatedLeads = await ValidateAndDeduplicateLeadsAsync(allLeads);

            return new ProcessedLeadsResult
            {
                ValidLeads = validatedLeads,
                Ads = ads.DistinctBy(a => a.Id).ToList(),
                Forms = forms.DistinctBy(f => f.Id).ToList(),
                IntegrationAccounts = integrationAccounts.DistinctBy(i => i.Id).ToList(),
                FetchedLeadsCount = fetchedLeadsCount
            };
        }

        private async Task<DataCache> LoadRequiredDataAsync(List<dynamic> subscribedAdsAndForms)
        {
            var facebookAuthResponseIds = subscribedAdsAndForms.Select(x => x.FacebookAuthResponseId).Distinct().ToList();
            var allIds = subscribedAdsAndForms.Select(x => x.Id).Distinct().ToList();
            var agencyNames = subscribedAdsAndForms.Select(x => x.Agency ?? "").Where(a => !string.IsNullOrEmpty(a)).Distinct().ToList();

            // Load all required data in parallel to minimize database round trips
            var integrationAccInfosTask = _integrationAccInfoRepo.ListAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(facebookAuthResponseIds), default);
            var adsDictTask = _fbAdsRepo.ListAsync(new AdsByIdsSpec(allIds));
            var formsDictTask = _facebookLeadGenFormRepo.ListAsync(new FormsByIdsSpec(allIds));
            var agencyDictTask = agencyNames.Any() ? _agencyRepo.ListAsync(new GetAllAgencyByNameSpec(agencyNames)) : Task.FromResult(new List<Agency>());

            await Task.WhenAll(integrationAccInfosTask, adsDictTask, formsDictTask, agencyDictTask);

            return new DataCache
            {
                IntegrationAccInfos = (await integrationAccInfosTask).ToDictionary(x => x.FacebookAccountId, x => x),
                AdsDict = (await adsDictTask).ToDictionary(x => x.Id, x => x),
                FormsDict = (await formsDictTask).ToDictionary(x => x.Id, x => x),
                AgencyDict = (await agencyDictTask).ToDictionary(x => x.Name, x => x)
            };
        }

        private async Task<Dictionary<string, List<FacebookLeadDto>>> FetchFacebookLeadsAsync(List<dynamic> subscribedAdsAndForms, DateTime? fromDate, DateTime? toDate)
        {
            var fbAuthGroups = subscribedAdsAndForms
                .GroupBy(x => new { Id = x.FacebookId ?? "", Token = x.FacebookUserOrPageAccessToken ?? "" })
                .ToDictionary(g => g.Key, g => g.ToList());

            var fbLeadsCache = new Dictionary<string, List<FacebookLeadDto>>();
            
            // Fetch leads for each auth group in parallel
            var fetchTasks = fbAuthGroups.Select(async group =>
            {
                var key = $"{group.Key.Id}::{group.Key.Token}";
                var fbBulkLeadInfo = await _facebookService.GetBulkLeadInfoAsync(group.Key.Id, group.Key.Token, fromDate, toDate);
                return new { Key = key, Data = fbBulkLeadInfo?.data ?? new List<FacebookLeadDto>() };
            });

            var results = await Task.WhenAll(fetchTasks);
            foreach (var result in results)
            {
                fbLeadsCache[result.Key] = result.Data;
            }

            return fbLeadsCache;
        }

        private async Task<SingleAdFormResult> ProcessSingleAdOrFormAsync(dynamic adOrForm, DataCache dataCache, Dictionary<string, List<FacebookLeadDto>> fbLeadsCache, ProcessingContext context)
        {
            var integrationAccountInfo = dataCache.IntegrationAccInfos.GetValueOrDefault(adOrForm.FacebookAuthResponseId);
            var ad = dataCache.AdsDict.GetValueOrDefault(adOrForm.Id);
            var form = dataCache.FormsDict.GetValueOrDefault(adOrForm.Id);
            var agency = dataCache.AgencyDict.GetValueOrDefault(adOrForm.Agency ?? "");

            var cacheKey = $"{adOrForm.FacebookId ?? ""}::{adOrForm.FacebookUserOrPageAccessToken ?? ""}";
            var fbLeads = fbLeadsCache.GetValueOrDefault(cacheKey) ?? new();
            
            if (!fbLeads.Any())
                return new SingleAdFormResult { FetchedCount = 0 };

            var leads = fbLeads.MapToGenericLeads(ad, form, context.GlobalSettings?.IsInstagramSourceEnabled ?? false, context.GlobalSettings)
                              .DistinctBy(l => l?.ContactNo ?? "").ToList();

            // Filter out leads with existing contacts (batch validation)
            var validLeads = await FilterExistingContactsAsync(leads);
            
            // Process each valid lead
            foreach (var lead in validLeads)
            {
                await EnrichLeadDataAsync(lead, ad, form, integrationAccountInfo, agency, adOrForm, context);
            }

            return new SingleAdFormResult
            {
                Leads = validLeads,
                Ad = ad,
                Form = form,
                IntegrationAccount = integrationAccountInfo,
                FetchedCount = fbLeads.Count
            };
        }

        private async Task<List<Lead>> FilterExistingContactsAsync(List<Lead> leads)
        {
            if (!leads.Any()) return leads;

            var contactNosWithoutCode = leads.Select(l =>
            {
                var contact = l.ContactNo ?? "";
                var code = l.CountryCode ?? "";
                return contact.StartsWith(code) ? contact.Substring(code.Length) : contact;
            }).Distinct().ToList();

            // Batch check for existing contacts
            var existingContacts = new HashSet<string>();
            try
            {
                var duplicateContacts = await _leadRepo.ListAsync(new DuplicateLeadForFacebookBulkFetchSpec(string.Join(",", contactNosWithoutCode)));
                existingContacts = duplicateContacts.Select(d => d.ContactNo).ToHashSet();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking duplicate contacts: {ex.Message}");
            }

            return leads.Where(l => !existingContacts.Any(ec => l.ContactNo.Contains(ec))).ToList();
        }

        private async Task EnrichLeadDataAsync(Lead lead, FacebookAdsInfo? ad, FacebookLeadGenForm? form, IntegrationAccountInfo? integrationAccountInfo, Agency? agency, dynamic adOrForm, ProcessingContext context)
        {
            lead.Name = string.IsNullOrWhiteSpace(lead.Name) ? "Facebook Enquiry" : lead.Name.Trim();
            lead.LeadNumber = lead.Name[0] + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            lead.CustomLeadStatus = context.CustomStatus ?? context.DefaultStatus;
            lead.TagInfo = new();
            lead.AgencyName = adOrForm.AgencyName;
            lead.Agencies = agency != null ? new List<Agency> { agency } : lead.Agencies;
            lead.AccountId = integrationAccountInfo?.Id ?? Guid.Empty;
            lead.Id = Guid.NewGuid();
            lead.CreatedBy = context.UserId;
            lead.LastModifiedBy = context.UserId;

            // Get user assignment and project
            var userAssignmentAndProject = await GetUserAssignmentAsync(ad, form, integrationAccountInfo);
            var existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string> { lead.ContactNo ?? "Invalid" }));
            var assignToRes = userAssignmentAndProject.UserAssignment != null 
                ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) 
                : (Guid.Empty, false);
            
            lead.AssignTo = assignToRes.Item1;
            _isDupicateUnassigned = assignToRes.Item2;
            
            if (userAssignmentAndProject.Project != null)
            {
                lead.Projects ??= new List<Lrb.Domain.Entities.Project>();
                lead.Projects.Add(userAssignmentAndProject.Project);
            }
        }

        private async Task<(UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project)> GetUserAssignmentAsync(FacebookAdsInfo? ad, FacebookLeadGenForm? form, IntegrationAccountInfo? integrationAccountInfo)
        {
            if (ad != null)
                return await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbAdsRepo: _fbAdsRepo);
            
            if (form != null)
                return await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(form.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbFormRepo: _facebookLeadGenFormRepo);
            
            return await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(integrationAccountInfo?.Id ?? Guid.Empty, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, integrationAccRepo: _integrationAccInfoRepo);
        }

        private async Task<List<Lead>> ValidateAndDeduplicateLeadsAsync(List<Lead> allLeads)
        {
            var uniqueLeads = allLeads.DistinctBy(i => i.ContactNo).ToList();

            // Process duplicate details in batch
            var contactNumbers = uniqueLeads.Select(l => l.ContactNo).ToList();
            var alternateContactNumbers = uniqueLeads.Select(l => l.AlternateContactNo).Where(c => !string.IsNullOrEmpty(c)).ToList();

            // Batch fetch parent leads
            var parentLeadsDict = new Dictionary<string, Lead>();
            if (contactNumbers.Any() || alternateContactNumbers.Any())
            {
                var parentLeads = await _leadRepo.ListAsync(new GetRootLeadsSpec(contactNumbers.Concat(alternateContactNumbers).ToList()));
                foreach (var parentLead in parentLeads)
                {
                    if (!string.IsNullOrEmpty(parentLead.ContactNo))
                        parentLeadsDict[parentLead.ContactNo] = parentLead;
                    if (!string.IsNullOrEmpty(parentLead.AlternateContactNo))
                        parentLeadsDict[parentLead.AlternateContactNo] = parentLead;
                }
            }

            // Update duplicate details and collect parent leads to update
            var parentLeadsToUpdate = new List<Lead>();
            foreach (var lead in uniqueLeads)
            {
                var parentLead = parentLeadsDict.GetValueOrDefault(lead.ContactNo) ?? parentLeadsDict.GetValueOrDefault(lead.AlternateContactNo);
                if (parentLead != null)
                {
                    lead.RootId = parentLead.Id;
                    lead.DuplicateLeadVersion = "D" + (parentLead.ChildLeadsCount + 1);
                    lead.ParentLeadId = parentLead.Id;
                    parentLead.ChildLeadsCount += 1;

                    if (!parentLeadsToUpdate.Any(p => p.Id == parentLead.Id))
                        parentLeadsToUpdate.Add(parentLead);
                }
            }

            // Batch update parent leads
            if (parentLeadsToUpdate.Any())
            {
                try
                {
                    await _leadRepo.UpdateRangeAsync(parentLeadsToUpdate);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error updating parent leads: {ex.Message}");
                }
            }

            return uniqueLeads;
        }

        private async Task SaveLeadsAndUpdateEntitiesAsync(ProcessedLeadsResult processedLeads, ProcessingContext context)
        {
            // Create lead histories
            var uniqueLeadDtos = processedLeads.ValidLeads.Adapt<List<ViewLeadDto>>();
            var leadHistories = uniqueLeadDtos.Select(i => LeadHistoryHelper.LeadHistoryMapper(i)).ToList();

            // Save leads and histories using bulk operations
            await _leadRepo.AddRangeAsync(processedLeads.ValidLeads);
            await _leadHistoryRepo.AddRangeAsync(leadHistories);

            // Update counts and platform statistics
            await UpdateEntityCountsAsync(processedLeads, context);
            await UpdatePlatformCountsAsync(processedLeads, context);

            // Batch update entities
            if (processedLeads.Ads.Any())
                await _fbAdsRepo.UpdateRangeAsync(processedLeads.Ads);

            if (processedLeads.Forms.Any())
                await _facebookLeadGenFormRepo.UpdateRangeAsync(processedLeads.Forms);

            if (processedLeads.IntegrationAccounts.Any())
                await _integrationAccInfoRepo.UpdateRangeAsync(processedLeads.IntegrationAccounts);
        }

        private async Task UpdateEntityCountsAsync(ProcessedLeadsResult processedLeads, ProcessingContext context)
        {
            var uniqueLeads = processedLeads.ValidLeads;

            // Update ads and forms lead counts
            foreach (var ad in processedLeads.Ads)
            {
                var relatedLeads = uniqueLeads.Where(i =>
                    (i.Notes?.Contains(ad.AdName ?? "Invalid Name", StringComparison.InvariantCultureIgnoreCase) ?? false) ||
                    (i.ConfidentialNotes?.Contains(ad.AdName ?? "Invalid Name", StringComparison.InvariantCultureIgnoreCase) ?? false));
                ad.LeadsCount += relatedLeads.Count();
            }

            foreach (var form in processedLeads.Forms)
            {
                var relatedLeads = uniqueLeads.Where(i =>
                    (i.Notes?.Contains(form.Name, StringComparison.InvariantCultureIgnoreCase) ?? false) ||
                    (i.ConfidentialNotes?.Contains(form.Name, StringComparison.InvariantCultureIgnoreCase) ?? false));
                form.TotalLeadsCount += relatedLeads.Count();
            }

            // Update integration account lead counts
            foreach (var intgrAcc in processedLeads.IntegrationAccounts)
            {
                intgrAcc.LeadCount += uniqueLeads.Count;
            }
        }

        private async Task UpdatePlatformCountsAsync(ProcessedLeadsResult processedLeads, ProcessingContext context)
        {
            var uniqueLeads = processedLeads.ValidLeads;
            var platformCountDtos = new Dictionary<Guid, FbPlatformCountDto>();

            // Calculate platform counts
            foreach (var ad in processedLeads.Ads)
            {
                var relatedLeads = uniqueLeads.Where(i =>
                    (i.Notes?.Contains(ad.AdName ?? "Invalid Name", StringComparison.InvariantCultureIgnoreCase) ?? false) ||
                    (i.ConfidentialNotes?.Contains(ad.AdName ?? "Invalid Name", StringComparison.InvariantCultureIgnoreCase) ?? false));

                if (!platformCountDtos.ContainsKey(ad.FacebookAuthResponseId))
                    platformCountDtos[ad.FacebookAuthResponseId] = new FbPlatformCountDto { FacebookAuthResponseId = ad.FacebookAuthResponseId };

                var platformDto = platformCountDtos[ad.FacebookAuthResponseId];
                platformDto.InstaLeadCount += relatedLeads.Count(i => i.Enquiries?[0]?.LeadSource == LeadSource.Instagram);
                platformDto.FbLeadCount += relatedLeads.Count(i => i.Enquiries?[0]?.LeadSource == LeadSource.Facebook);
            }

            foreach (var form in processedLeads.Forms)
            {
                var relatedLeads = uniqueLeads.Where(i =>
                    (i.Notes?.Contains(form.Name, StringComparison.InvariantCultureIgnoreCase) ?? false) ||
                    (i.ConfidentialNotes?.Contains(form.Name, StringComparison.InvariantCultureIgnoreCase) ?? false));

                if (!platformCountDtos.ContainsKey(form.FacebookAuthResponseId))
                    platformCountDtos[form.FacebookAuthResponseId] = new FbPlatformCountDto { FacebookAuthResponseId = form.FacebookAuthResponseId };

                var platformDto = platformCountDtos[form.FacebookAuthResponseId];
                platformDto.InstaLeadCount += relatedLeads.Count(i => i.Enquiries?[0]?.LeadSource == LeadSource.Instagram);
                platformDto.FbLeadCount += relatedLeads.Count(i => i.Enquiries?[0]?.LeadSource == LeadSource.Facebook);
            }

            // Batch update Facebook auth responses
            var authResponsesToUpdate = new List<FacebookAuthResponse>();
            foreach (var platformDto in platformCountDtos.Values.Where(p => p.InstaLeadCount > 0 || p.FbLeadCount > 0))
            {
                var fbAuthResponse = await _facebookAuthResponseRepo.GetByIdAsync(platformDto.FacebookAuthResponseId);
                if (fbAuthResponse != null)
                {
                    fbAuthResponse.InstaLeadCount += platformDto.InstaLeadCount;
                    fbAuthResponse.FbLeadCount += platformDto.FbLeadCount;
                    authResponsesToUpdate.Add(fbAuthResponse);
                }
            }

            if (authResponsesToUpdate.Any())
                await _facebookAuthResponseRepo.UpdateRangeAsync(authResponsesToUpdate);
        }

        private async Task FinalizeProcessingAsync(FbBulkLeadFetchTracker tracker, ProcessedLeadsResult processedLeads, Guid userId)
        {
            tracker.FetchedLeadsCount = processedLeads.FetchedLeadsCount;
            tracker.UniqueLeadsCount = processedLeads.ValidLeads.Count;
            tracker.StoredLeadsCount = processedLeads.ValidLeads.Count;
            tracker.Status = UploadStatus.Completed;
            tracker.LastModifiedBy = userId;

            await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
            Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Processing completed. Stored {processedLeads.ValidLeads.Count} leads.");
        }

        private async Task HandleProcessingErrorAsync(Guid trackerId, Guid userId, Exception ex)
        {
            Console.WriteLine($"FacebookBulkLeadsFetchHandler() Error Message: {ex?.InnerException?.Message ?? ex?.Message}");

            var tracker = await _fbBulkLeadFetchTrackerRepository.GetByIdAsync(trackerId);
            if (tracker != null)
            {
                tracker.Status = UploadStatus.Failed;
                tracker.Error = JsonConvert.SerializeObject(ex, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore });
                tracker.LastModifiedBy = userId;
                await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
            }

            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "FunctionEntryPoint -> FacebookBulkLeadsFetchHandler()",
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
        }
    }

    // Helper classes for better organization
    public class ProcessingContext
    {
        public FbBulkLeadFetchTracker Tracker { get; set; } = default!;
        public GlobalSettings? GlobalSettings { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public CustomMasterLeadStatus? CustomStatus { get; set; }
        public CustomMasterLeadStatus? DefaultStatus { get; set; }
        public Guid UserId { get; set; }
    }

    public class DataCache
    {
        public Dictionary<Guid, IntegrationAccountInfo> IntegrationAccInfos { get; set; } = new();
        public Dictionary<Guid, FacebookAdsInfo> AdsDict { get; set; } = new();
        public Dictionary<Guid, FacebookLeadGenForm> FormsDict { get; set; } = new();
        public Dictionary<string, Agency> AgencyDict { get; set; } = new();
    }

    public class ProcessedLeadsResult
    {
        public List<Lead> ValidLeads { get; set; } = new();
        public List<FacebookAdsInfo> Ads { get; set; } = new();
        public List<FacebookLeadGenForm> Forms { get; set; } = new();
        public List<IntegrationAccountInfo> IntegrationAccounts { get; set; } = new();
        public int FetchedLeadsCount { get; set; }
    }

    public class SingleAdFormResult
    {
        public List<Lead> Leads { get; set; } = new();
        public FacebookAdsInfo? Ad { get; set; }
        public FacebookLeadGenForm? Form { get; set; }
        public IntegrationAccountInfo? IntegrationAccount { get; set; }
        public int FetchedCount { get; set; }
    }
}
