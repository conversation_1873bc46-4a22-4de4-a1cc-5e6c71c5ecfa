﻿using Lrb.Application.Agency;
using Lrb.Application.Agency.Web;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.Integration.Web.Automation;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Mapster;
using MessagePack;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using System.Threading;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : FBCommonHandler, IFunctionEntryPoint
    {
        public async Task FacebookBulkLeadsFetchHandlerV1(InputPayload input)
        {
            var tenantId = input.TenantId;
            var userId = input.CurrentUserId;
            var trackerId = input.TrackerId;
            if (!string.IsNullOrWhiteSpace(tenantId))
            {
                GlobalSettings? globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec());

                var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(default));
                var tracker = await _fbBulkLeadFetchTrackerRepository.GetByIdAsync(trackerId);
                if (tracker == null)
                {
                    return;
                }
                tracker.LastModifiedBy = userId;

                //tracker Update
                tracker.Status = UploadStatus.Started;
                await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
                Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                try
                {
                    var subscribedAdsAndForms = await _facebookService.GetAdsAndFormsWithUserOrPageAccessTokenAsync(tenantId);
                    //tracker Update
                    tracker.ActiveAdsCount = subscribedAdsAndForms?.Count(i => i?.Type?.Contains("ad", StringComparison.InvariantCultureIgnoreCase) ?? false) ?? 0;
                    tracker.ActiveFormsCount = subscribedAdsAndForms?.Count(i => i?.Type?.Contains("form", StringComparison.InvariantCultureIgnoreCase) ?? false) ?? 0;
                    tracker.Status = UploadStatus.InProgress;
                    await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
                    Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    if (subscribedAdsAndForms?.Any() ?? false)
                    {
                        var fromDate = tracker.FromDate;
                        var toDate = tracker.ToDate;
                        var timeZone = JsonConvert.DeserializeObject<CommonTimeZoneDto>(input.JsonData ?? string.Empty) ?? new();
                        if (tracker?.FromDate != null)
                        {
                            fromDate = ToParticularTimeZone(tracker.FromDate ?? DateTime.UtcNow.Date.ConvertFromDateToUtc(), timeZone.TimeZoneId, timeZone.BaseUTcOffset);

                        }
                        if (tracker?.ToDate != null)
                        {
                            toDate = ToParticularTimeZone(tracker.ToDate ?? DateTime.UtcNow, timeZone.TimeZoneId, timeZone.BaseUTcOffset);
                        }
                        subscribedAdsAndForms = subscribedAdsAndForms.OrderBy(x => x.Type).ToList();
                        int fetchedLeadsCount = 0;
                        int uniqueLeadsCount = 0;
                        int storedLeadsCount = 0;
                        List<LeadHistory> leadHistories = new();
                        List<Lead> allLeads = new();
                        List<FacebookAdsInfo> ads = new();
                        List<FacebookLeadGenForm> forms = new();
                        List<IntegrationAccountInfo> integrationAccounts = new();
                        List<Lead> totalFetchedFbLeads = new();
                        var customStatus = (await _customMastereadStatus.FirstOrDefaultAsync(new GetDefaultStatusSpec()));
                        var defaultStatus = await _customMastereadStatus.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string> { "new" }), CancellationToken.None);
                        var integrationAccInfos = (await _integrationAccInfoRepo.ListAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(subscribedAdsAndForms.Select(x => x.FacebookAuthResponseId).Distinct().ToList()), default)).ToDictionary(x => x.FacebookAccountId, x => x);

                        var allIds = subscribedAdsAndForms.Select(x => x.Id).Distinct().ToList();
                        var adsDict = (await _fbAdsRepo.ListAsync(new AdsByIdsSpec(allIds))).ToDictionary(x => x.Id, x => x);
                        var formsDict = (await _facebookLeadGenFormRepo.ListAsync(new FormsByIdsSpec(allIds))).ToDictionary(x => x.Id, x => x);
                        var agencyDict = (await _agencyRepo.ListAsync(new GetAllAgencyByNameSpec(subscribedAdsAndForms.Select(x => x.Agency ?? "").Distinct().ToList()))).ToDictionary(x => x.Name, x => x);
                        var fbAuthGroups = subscribedAdsAndForms.GroupBy(x => new { Id = x.FacebookId ?? "", Token = x.FacebookUserOrPageAccessToken ?? "" }).ToDictionary(g => g.Key, g => g.ToList());
                        var fbLeadsCache = new Dictionary<string, List<FacebookLeadDto>>();
                        foreach (var group in fbAuthGroups)
                        {
                            var key = $"{group.Key.Id}::{group.Key.Token}";
                            var fbBulkLeadInfo = await _facebookService.GetBulkLeadInfoAsync(group.Key.Id, group.Key.Token, fromDate, toDate);
                            fbLeadsCache[key] = fbBulkLeadInfo?.data ?? new();
                        }

                        foreach (var adOrForm in subscribedAdsAndForms)
                        {
                            var integrationAccountInfo = integrationAccInfos.GetValueOrDefault(adOrForm.FacebookAuthResponseId);
                            var ad = adsDict.GetValueOrDefault(adOrForm.Id);
                            var form = formsDict.GetValueOrDefault(adOrForm.Id);
                            var agency = agencyDict.GetValueOrDefault(adOrForm.Agency ?? "");

                            var cacheKey = $"{adOrForm.FacebookId ?? ""}::{adOrForm.FacebookUserOrPageAccessToken ?? ""}";
                            var fbLeads = fbLeadsCache.GetValueOrDefault(cacheKey) ?? new();
                            if (!fbLeads.Any()) continue;

                            fetchedLeadsCount += fbLeads.Count;
                            tracker.FetchedLeadsCount = fetchedLeadsCount;
                            var leads = fbLeads.MapToGenericLeads(ad, form, globalSettings?.IsInstagramSourceEnabled ?? false, globalSettings)
                                               .DistinctBy(l => l?.ContactNo ?? "").ToList();

                            var contactNosWithoutCode = leads.Select(l =>
                            {
                                var contact = l.ContactNo ?? "";
                                var code = l.CountryCode ?? "";
                                return contact.StartsWith(code) ? contact.Substring(code.Length) : contact;
                            }).Distinct().ToList();
                            foreach (var contactNo in contactNosWithoutCode)
                            {
                                try
                                {
                                    var duplicateContact = await _leadRepo.ListAsync(new DuplicateLeadForFacebookBulkFetchSpec(contactNo));
                                    if (duplicateContact.Count > 0)
                                    {
                                        leads?.RemoveAll(i => i.ContactNo.Contains(contactNo));
                                    }
                                }
                                catch
                                {
                                }
                            }
                            var newLeads = leads.Where(l => !string.IsNullOrWhiteSpace(l?.ContactNo)).ToList();
                            if (!newLeads.Any()) continue;
                            foreach (var lead in newLeads)
                            {

                                lead.Name = string.IsNullOrWhiteSpace(lead.Name) ? "Facebook Enquiry" : lead.Name.Trim();
                                lead.LeadNumber = lead.Name[0] + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                                lead.CustomLeadStatus = customStatus ?? defaultStatus;
                                lead.TagInfo = new();
                                lead.AgencyName = adOrForm.AgencyName;
                                lead.Agencies = agency != null ? new List<Agency> { agency } : lead.Agencies;
                                lead.AccountId = integrationAccountInfo?.Id ?? Guid.Empty;
                                #region Automation
                                var userAssignmentAndProject = ad != null
                                    ? await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbAdsRepo: _fbAdsRepo)
                                    : form != null
                                        ? await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(form.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbFormRepo: _facebookLeadGenFormRepo)
                                        : await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(integrationAccountInfo?.Id ?? Guid.Empty, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, integrationAccRepo: _integrationAccInfoRepo);

                                var existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string> { lead.ContactNo ?? "Invalid" }));
                                var assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                                lead.AssignTo = assignToRes.Item1;
                                _isDupicateUnassigned = assignToRes.Item2;
                                if (userAssignmentAndProject.Project != null)
                                {
                                    lead.Projects ??= new List<Lrb.Domain.Entities.Project>();
                                    lead.Projects.Add(userAssignmentAndProject.Project);
                                }
                                #endregion
                                lead.Id = Guid.NewGuid();
                                lead.CreatedBy = userId;
                                lead.LastModifiedBy = userId;

                                if (!allLeads.Any(existing => existing.ContactNo.EndsWith(
                                    lead.ContactNo.StartsWith(lead.CountryCode) ? lead.ContactNo.Substring(lead.CountryCode.Length) : lead.ContactNo)))
                                {
                                    allLeads.Add(lead);
                                }
                            }

                            if (ad != null) ads.Add(ad);
                            if (form != null) forms.Add(form);
                            if (integrationAccountInfo != null) integrationAccounts.Add(integrationAccountInfo);
                        }
                        List<Lead> totalExistingLeadsInDateRange = await _leadRepo.ListAsync(new LeadsBySourceAndDateRangeSpec(fromDate, toDate, LeadSource.Facebook));
                        var totalDistinctExistingLeadsInDateRange = totalExistingLeadsInDateRange.DistinctBy(i => i.ContactNo).ToList();
                        Console.WriteLine($"Total unique leads present the DB in the date range given = {totalDistinctExistingLeadsInDateRange.Count}");
                        var totalFetchedUniqueLeads = totalFetchedFbLeads.DistinctBy(i => i.ContactNo).ToList();
                        Console.WriteLine($"Total fetched unique leads from Facebook in the date range given = {totalDistinctExistingLeadsInDateRange.Count}");
                        Console.WriteLine($"The difference between Fetched Leads and Existing Leads in the date range given, Unique(Fetched - Existing) = {totalDistinctExistingLeadsInDateRange.Count - totalDistinctExistingLeadsInDateRange.Count}");
                        var uniqueLeads = allLeads.DistinctBy(i => i.ContactNo).ToList();
                        Console.WriteLine($"Total unique and valid leads from Facebook after removing the leads, which were created in same time on Facebook and CRM = {uniqueLeads.Count}");
                        var uniqueLeadDtos = uniqueLeads.Adapt<List<ViewLeadDto>>();
                        leadHistories = uniqueLeadDtos.Select(i => LeadHistoryHelper.LeadHistoryMapper(i)).ToList();
                        foreach (var lead in uniqueLeads)
                        {
                            #region DuplicateDetails
                            var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), default);
                            if (parentLead != null)
                            {
                                lead.RootId = parentLead.Id;
                                lead.DuplicateLeadVersion = "D" + (parentLead.ChildLeadsCount + 1);
                                lead.ParentLeadId = parentLead.Id;
                                parentLead.ChildLeadsCount += 1;
                                try
                                {
                                    await _leadRepo.UpdateAsync(parentLead);
                                }
                                catch { }
                            }
                            #endregion
                        }
                        await _leadRepo.AddRangeAsync(uniqueLeads);
                        await _leadHistoryRepo.AddRangeAsync(leadHistories);

                        ads = ads.DistinctBy(i => i.Id).ToList();
                        forms = forms.DistinctBy(i => i.Id).ToList();
                        integrationAccounts = integrationAccounts.DistinctBy(i => i.Id).ToList();
                        var platfromCountDtos = subscribedAdsAndForms.Select(i => new FbPlatformCountDto
                        {
                            FacebookAuthResponseId = i.FacebookAuthResponseId
                        }).ToList();
                        foreach (var adOrForm in subscribedAdsAndForms)
                        {
                            var ad = ads.FirstOrDefault(i => i.Id == adOrForm.Id);
                            if (ad != null)
                            {
                                var relatedLeads = uniqueLeads.Where(i => (i.Notes?.Contains(ad.AdName ?? "Invalid Name", StringComparison.InvariantCultureIgnoreCase) ?? false) || (i.ConfidentialNotes?.Contains(ad.AdName ?? "Invalid Name", StringComparison.InvariantCultureIgnoreCase) ?? false));
                                ad.LeadsCount += relatedLeads.Count();
                                var platformCountDto = platfromCountDtos.FirstOrDefault(i => i.FacebookAuthResponseId == ad.FacebookAuthResponseId);
                                if (platformCountDto != null)
                                {
                                    platformCountDto.InstaLeadCount += relatedLeads.Where(i => i.Enquiries?[0]?.LeadSource == LeadSource.Instagram).Count();
                                    platformCountDto.FbLeadCount += relatedLeads.Where(i => i.Enquiries?[0]?.LeadSource == LeadSource.Facebook).Count();
                                }
                            }
                            var form = forms.FirstOrDefault(i => i.Id == adOrForm.Id);
                            if (form != null)
                            {
                                var relatedLeads = uniqueLeads.Where(i => (i.Notes?.Contains(form.Name, StringComparison.InvariantCultureIgnoreCase) ?? false) || (i.ConfidentialNotes?.Contains(form.Name, StringComparison.InvariantCultureIgnoreCase) ?? false));
                                form.TotalLeadsCount += relatedLeads.Count();
                                var platformCountDto = platfromCountDtos.FirstOrDefault(i => i.FacebookAuthResponseId == adOrForm.FacebookAuthResponseId);
                                if (platformCountDto != null)
                                {
                                    platformCountDto.InstaLeadCount += relatedLeads.Where(i => i.Enquiries?[0]?.LeadSource == LeadSource.Instagram).Count();
                                    platformCountDto.FbLeadCount += relatedLeads.Where(i => i.Enquiries?[0]?.LeadSource == LeadSource.Facebook).Count();
                                }
                            }
                        }
                        foreach (var intgrAcc in integrationAccounts)
                        {
                            try
                            {
                                intgrAcc.LeadCount += uniqueLeads?.Count() ?? 0;
                            }
                            catch (Exception e) { }
                        }
                        foreach (var platformDto in platfromCountDtos)
                        {
                            var fbAuthResponse = await _facebookAuthResponseRepo.GetByIdAsync(platformDto.FacebookAuthResponseId);
                            if (fbAuthResponse != null && (platformDto.InstaLeadCount > 0 || platformDto.FbLeadCount > 0))
                            {
                                fbAuthResponse.InstaLeadCount += platformDto.InstaLeadCount;
                                fbAuthResponse.FbLeadCount += platformDto.FbLeadCount;
                                  await _facebookAuthResponseRepo.UpdateAsync(fbAuthResponse);
                            }
                        }
                        await _fbAdsRepo.UpdateRangeAsync(ads);
                        await _facebookLeadGenFormRepo.UpdateRangeAsync(forms);
                        await _integrationAccInfoRepo.UpdateRangeAsync(integrationAccounts);
                        uniqueLeadsCount += uniqueLeads.Count;
                        storedLeadsCount += uniqueLeads.Count;
                        tracker.FetchedLeadsCount = fetchedLeadsCount;
                        tracker.UniqueLeadsCount = uniqueLeadsCount;
                        tracker.StoredLeadsCount = storedLeadsCount;
                        tracker.Status = UploadStatus.Completed;
                        await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
                        Console.WriteLine($"FacebookBulkLeadsFetchHandler() -> Tracker: {JsonConvert.SerializeObject(tracker, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    }
                }
                catch (Exception e)
                {
                     Console.WriteLine($"FacebookBulkLeadsFetchHandler() Error Message: {e?.InnerException?.Message ?? e?.Message}");
                    tracker.Status = UploadStatus.Failed;
                    tracker.Error = JsonConvert.SerializeObject(e, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore });
                    await _fbBulkLeadFetchTrackerRepository.UpdateAsync(tracker);
                    var error = new LrbError()
                    {
                        ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                        ErrorSource = e?.Source,
                        StackTrace = e?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "FunctionEntryPoint -> FacebookBulkLeadsFetchHandler()",
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
            }
        }
    }
}