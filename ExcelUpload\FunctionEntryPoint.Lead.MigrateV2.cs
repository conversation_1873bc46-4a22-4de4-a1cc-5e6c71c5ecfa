﻿using Dapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Agency;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.ChannelPartner;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Mobile.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project;
using Lrb.Application.Property;
using Lrb.Application.Reports.Web;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Shared.Utils;
using Mapster;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Graph;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Npgsql;
using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Reflection;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : IFunctionEntryPoint
    {
        public async Task<DataTable> GetDataTableAsync(LeadMigrateTracker leadMigrateUploadTracker)
        {
            #region Convert file to Datatable
            DataTable dataTable = new();
            List<InvalidData> invalids = new();
            int totalRows = 0;
            if (!string.IsNullOrWhiteSpace(leadMigrateUploadTracker.S3BucketKey))
            {
                Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", leadMigrateUploadTracker.S3BucketKey);
                if (leadMigrateUploadTracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                {
                    using MemoryStream memoryStream = new();
                    fileStream.CopyTo(memoryStream);
                    dataTable = CSVHelper.CSVToDataTable(memoryStream);
                }
                else
                {
                    dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, leadMigrateUploadTracker.SheetName);
                }
                totalRows = dataTable.Rows.Count;
                for (int i = totalRows - 1; i >= 0; i--)
                {
                    DataRow row = dataTable.Rows[i];
                    if (row.ItemArray.All(i => string.IsNullOrEmpty(i?.ToString())))
                    {
                        row.Delete();
                    }
                    else if (string.IsNullOrEmpty(row[leadMigrateUploadTracker.MappedColumnsData?[DataColumns.Name] ?? string.Empty].ToString()) && string.IsNullOrEmpty(row[leadMigrateUploadTracker.MappedColumnsData?[DataColumns.ContactNo] ?? string.Empty].ToString()))
                    {
                        var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i?.ToString())));
                        var invalidData = new InvalidData
                        {
                            Errors = "Contact number and name are empty.",
                            Notes = notes
                        };
                        if (!invalids.Any(i => i.Notes == invalidData.Notes))
                        {
                            invalids.Add(invalidData);
                        }
                        row.Delete();
                    }
                }
                if (dataTable.Rows.Count <= 0)
                {
                    throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                }
                totalRows = dataTable.Rows.Count;
                Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
            }
            #endregion
            return dataTable;
        }

        public async Task<MasterItems> GetMasterItemsAsync(DataTable dataTable, LeadMigrateTracker leadMigrateUploadTracker, string currentUserId, string tenantId)
        {
            #region featching data from excel
            List<string> properties = new List<string>();
            List<string> projects = new List<string>();
            List<string> agencies = new List<string>();
            List<string> channelPartners = new List<string>();
            List<string>? assignedToUsers = new();
            List<string> campaigns = new List<string>();
            if (((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false))
                || ((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false))
                || ((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false))
                || ((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false))
                || ((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AssignToUser) ?? false))
                || ((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AssignToSecondaryUser) ?? false))
                || ((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false))
                || ((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.SourcingManager) ?? false))
                || ((leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ClosingManager) ?? false)))
            {
                var isPropertyPresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false);
                var isProjectPresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false);
                var isAgencyNamePresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false);
                var isChannelPresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false);
                var isassignedToUserPresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AssignToUser) ?? false);
                var isCamapignPresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false);
                var isSourcingManagerPresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.SourcingManager) ?? false);
                var isClosingManagerPresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ClosingManager) ?? false);
                var isSecondaryAssignedToUserPresent = (leadMigrateUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AssignToSecondaryUser) ?? false);

                dataTable.AsEnumerable().ToList().ForEach(row =>
                {
                    if (isPropertyPresent)
                    {
                        var propertyName = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.Property]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(propertyName))
                        {
                            var propertyNames = propertyName.Split(',')
                                                          .Select(p => p.Trim())
                                                          .Where(p => !string.IsNullOrWhiteSpace(p));
                            properties.AddRange(propertyNames);
                        }
                    }
                    if (isProjectPresent)
                    {
                        var projectName = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.Project]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(projectName))
                        {
                            var projectNames = projectName.Split(',')
                                                          .Select(p => p.Trim())
                                                          .Where(p => !string.IsNullOrWhiteSpace(p));

                            projects.AddRange(projectNames);
                        }
                    }
                    if (isAgencyNamePresent)
                    {
                        var agencyName = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.AgencyName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(agencyName))
                        {
                            agencies.Add(agencyName.Trim());
                        }
                    }
                    if (isChannelPresent)
                    {
                        var cpName = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.ChannelPartnerName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(cpName))
                        {
                            channelPartners.Add(cpName.Trim());
                        }
                    }
                    if (isassignedToUserPresent)
                    {
                        var assinedUser = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.AssignToUser]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(assinedUser))
                        {
                            assignedToUsers.Add(assinedUser.Trim().ToLower());
                        }
                    }
                    if (isSecondaryAssignedToUserPresent)
                    {
                        var secondarAssinedUser = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.AssignToSecondaryUser]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(secondarAssinedUser))
                        {
                            assignedToUsers.Add(secondarAssinedUser.Trim().ToLower());
                        }
                    }
                    if (isCamapignPresent)
                    {
                        var campaignName = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.CampaignName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(campaignName))
                        {
                            campaigns.Add(campaignName.Trim());
                        }
                    }
                    if (isClosingManagerPresent)
                    {
                        var sourcingUser = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.SourcingManager]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(sourcingUser))
                        {
                            assignedToUsers.Add(sourcingUser.Trim().ToLower());
                        }
                    }
                    if (isSourcingManagerPresent)
                    {
                        var closingUser = row[leadMigrateUploadTracker.MappedColumnsData[DataColumns.ClosingManager]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(closingUser))
                        {
                            assignedToUsers.Add(closingUser.Trim().ToLower());
                        }
                    }
                });
            }
            #endregion
            var masterItems = new MasterItems();
            var cancellationToken = CancellationToken.None;
            var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
            masterItems.TenantDisplayPrefix = await _dapperRepository.GetDisplayIndexPrefixByTenantIdAsync(tenantId);
            #region Projects
            var allProjects = await _newProjectRepo.ListAsync(new GetAllProjectByNamesSpec(projects.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            allProjects = allProjects.GroupBy(p => p.Name.ToLower().Trim()).Select(g => g.First()).ToList();
            var remainingProjects = projects.Select(i => i).ToHashSet()
                .Where(project => !allProjects.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(project.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingProjects?.Any() ?? false)
            {
                var maxserial = await _dapperRepository.GetProjectMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var newprojects = remainingProjects
                    .Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        maxserial = serialNumber.Item2;
                        return new Project
                        {
                            Id = Guid.NewGuid(),
                            Name = i,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            SerialNo = serialNumber.Item1,
                        };
                    }).ToList();
                var newProjects = await CreateProejctsInDapperAsync(newprojects, connection, tenantId);
                if (newProjects.Item1 != null)
                {
                    allProjects.AddRange(newProjects.Item1);
                }
            }
            var deletedProjects = allProjects.Where(i => i.IsArchived).GroupBy(i => i.Name.ToLower().Trim()).Select(i => i.First()).ToList();
            allProjects.RemoveAll(p => deletedProjects.Any(d => d.Id == p.Id));
            if (deletedProjects?.Any() ?? false)
            {
                var restoredProjects = await RestoreProejctsInDapperAsync(deletedProjects, connection, tenantId);
                if (restoredProjects.Item1 != null)
                {
                    allProjects.AddRange(restoredProjects.Item1);
                }
            }
            masterItems.Projects = allProjects;
            #endregion

            #region Properties
            var allProperties = await _propertyRepo.ListAsync(new GetAllPropertyByTitlesSpec(properties.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            allProperties = allProperties.GroupBy(p => p.Title.ToLower().Trim()).Select(g => g.First()).ToList();
            var remainingProperties = properties.Select(i => i).ToHashSet()
                .Where(property => !string.IsNullOrEmpty(property) && !allProperties.ConvertAll(i => i.Title.ToLower().Trim().Replace(" ", "")).Contains(property.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingProperties?.Any() ?? false)
            {
                var maxserial = await _dapperRepository.GetPropertiesMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var newproperties = remainingProperties
                    .Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        maxserial = serialNumber.Item2;
                        return new Property
                        {
                            Id = Guid.NewGuid(),
                            Title = i,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            SerialNo = serialNumber.Item1,
                        };
                    }).ToList();
                var newProperties = await CreateProrpertyInDapperAsync(newproperties, connection, tenantId);
                if (newProperties.Item1 != null)
                {
                    allProperties.AddRange(newProperties.Item1);
                }
            }
            var deletedProperties = allProperties.Where(i => i.IsArchived).ToList();
            allProperties.RemoveAll(p => deletedProperties.Any(d => d.Id == p.Id));
            if (deletedProperties?.Any() ?? false)
            {
                var restoredProperties = await RestorePropertiesInDapperAsync(deletedProperties, connection, tenantId);
                if (restoredProperties.Item1 != null)
                {
                    allProperties.AddRange(restoredProperties.Item1);
                }
            }
            masterItems.Properties = allProperties;
            #endregion

            #region CommonData
            masterItems.PropetyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
            masterItems.AreaUnits = await _masterAreaUnitRepo.ListAsync(cancellationToken);
            masterItems.LeadStatuses = await _customMastereadStatus.ListAsync(CancellationToken.None);
            masterItems.GlobalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
            masterItems.SubSources = (await _dapperRepository.GetAllIntegrationSubSourceAsync<Lrb.Application.Lead.Web.SourceDto>(tenantId)).ToList();
            masterItems.Flags = await _flagRepo.ListAsync(cancellationToken);
            #endregion

            #region Users
            var userIds = new List<string> { leadMigrateUploadTracker.CreatedBy.ToString(),
                   leadMigrateUploadTracker.LastModifiedBy.ToString(),currentUserId }.ToList().ConvertAll(i => Guid.Parse(i));
            masterItems.HistoryUsers = await _userViewRepo.ListAsync(new UserViewByIdSpec(userIds));
            var users = await _userViewRepo.ListAsync(new GetUsersByUsernamesSpec(assignedToUsers.Distinct().ToList() ?? new()), cancellationToken);
            if (users?.Any() ?? false)
            {
                if (masterItems.Users == null)
                {
                    masterItems.Users = new List<UserView>();
                }
                masterItems.Users.AddRange(users);
                masterItems.HistoryUsers.AddRange(users);
            }

            #endregion

            #region Agencies
            var allAgencies = await _agencyRepo.ListAsync(new GetAllAgencyByNameSpec(agencies.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingAgencies = agencies.Select(i => i).ToHashSet()
                .Where(agency => !string.IsNullOrEmpty(agency) && !allAgencies.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(agency.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingAgencies?.Any() ?? false)
            {
                var newagencies = remainingAgencies.Select(i => new Agency { Id = Guid.NewGuid(), Name = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var newAgencies = await CreateAgencyInDapperAsync(newagencies, connection, tenantId);
                if (newAgencies.Item1 != null)
                {
                    allAgencies.AddRange(newAgencies.Item1);
                }
            }
            masterItems.Agencies = allAgencies;
            #endregion

            #region ChannelPartner
            var allChannelPartner = await _cpRepository.ListAsync(new GetAllChannelPartnerByNameSpec(channelPartners.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingChannelPartner = channelPartners.Select(i => i).ToHashSet()
                .Where(channelPartner => !string.IsNullOrEmpty(channelPartner) && !allChannelPartner.ConvertAll(i => i.FirmName.ToLower().Trim().Replace(" ", "")).Contains(channelPartner.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingChannelPartner?.Any() ?? false)
            {
                var newchannelPartner = remainingChannelPartner.Select(i => new ChannelPartner { Id = Guid.NewGuid(), FirmName = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var newChannelPartner = await CreateChannalPartnerInDapperAsync(newchannelPartner, connection, tenantId);
                if (newChannelPartner.Item1 != null)
                {
                    allChannelPartner.AddRange(newChannelPartner.Item1);
                }
            }
            masterItems.ChannelPartners = allChannelPartner;
            #endregion
            #region Campaign
            var allCampaigns = await _campaignRepo.ListAsync(new GetCampaignByNameSpec(campaigns.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingCampaigns = campaigns.Select(i => i).ToHashSet()
                .Where(campaign => !string.IsNullOrEmpty(campaign) && !allCampaigns.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(campaign.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingCampaigns?.Any() ?? false)
            {
                var newCampaign = remainingCampaigns.Select(i => new Campaign { Id = Guid.NewGuid(), Name = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var updatedCampaign = await CreateCampaignInDapperAsync(newCampaign, connection, tenantId);
                if (updatedCampaign.Item1 != null)
                {
                    allCampaigns.AddRange(updatedCampaign.Item1);
                }
            }
            masterItems.Campaigns = allCampaigns;
            #endregion

            return masterItems;
        }
        public async Task MigrateLeadHandlerV2(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            //GEt the Tracker by Id 
            LeadMigrateTracker? leadMigrateTracker = await _leadMigrateTrackerRepo.GetByIdAsync(input.TrackerId);
            Console.WriteLine($"handler() -> LeadMigrateTracker GetById(): {JsonConvert.SerializeObject(leadMigrateTracker)}");
            if (leadMigrateTracker != null)
            {
                try
                {
                    // updating tracker
                    leadMigrateTracker.MappedColumnsData = leadMigrateTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    leadMigrateTracker.Status = UploadStatus.Started;
                    leadMigrateTracker.LastModifiedBy = input.CurrentUserId;
                    leadMigrateTracker.CreatedBy = input.CurrentUserId;
                    leadMigrateTracker.SheetName = leadMigrateTracker.S3BucketKey.Split('/').Last() + "/" + leadMigrateTracker.SheetName;
                    var migrationType = leadMigrateTracker.MigrationType;
                    var currentUser = input.CurrentUserId;
                    await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                    Console.WriteLine($"handler() -> LeadMigrateTracker Updated Status: {leadMigrateTracker.Status} \n {JsonConvert.SerializeObject(leadMigrateTracker)}");

                    // retrieving the data from excel  
                    DataTable dataTable = await GetDataTableAsync(leadMigrateTracker);

                    //retrieving the master items  
                    var masterItems = await GetMasterItemsAsync(dataTable, leadMigrateTracker, input.CurrentUserId.ToString(), input.TenantId);

                    // Un Mapped Columns
                    var unMappedColumns = dataTable.GetUnmappedColumnNames(leadMigrateTracker.MappedColumnsData ?? new());

                    var results = dataTable.MigrateAsyncV2(leadMigrateTracker.MappedColumnsData, unMappedColumns, masterItems, leadMigrateTracker, _sourceRepo, currentUser, input.JsonData ?? string.Empty);
                    List<Lead> leads = results.Item1;
                    var invalids = results.Item2;
                    leads = leads.DistinctBy(i => i.ContactNo).ToList();
                    if (migrationType == LeadMigrationType.UpdateMissingInformation || migrationType == LeadMigrationType.OverideExisitingLeadInformation)
                    {
                        //update Tracker
                        leadMigrateTracker.Status = UploadStatus.InProgress;
                        leadMigrateTracker.TotalCount = dataTable.Rows.Count;
                        leadMigrateTracker.DistinctLeadCount = leads.Count();
                        leadMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        leadMigrateTracker.DuplicateCount = 0;
                        leadMigrateTracker.CreatedBy = input.CurrentUserId;
                        leadMigrateTracker.TotalUploadedCount = 0;

                        if (invalids.Any())
                        {
                            leadMigrateTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty" || i.Errors == "Parent Lead Not Found" || i.Errors == "Child Lead Not Found" || i.Errors == "Duplicate ContactNo" || i.Errors == "Invalid Source").Count();
                            byte[] bytes = CreateLeadHelper.CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = $"{input.TenantId}/Leads/Migrate";
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            leadMigrateTracker.InvalidDataS3BucketKey = key;
                        }

                        await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        BulkMigrateBackgroundDto backgroundDto = new();
                        if (leads?.Count > 0)
                        {
                            int leadsPerchunk = leads.Count > 5000 ? 5000 : leads.Count;
                            var chunks = leads.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkMigrateBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = leadMigrateTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Leads = new(chunk),
                                    UserViews = masterItems.Users?.ToList(),
                                    HistoryUsers = masterItems.HistoryUsers,
                                    MigrationType = migrationType
                                };
                                await V2UpdateDuplicateLeadsAsync(backgroundDto, masterItems);
                                chunkIndex++;
                            }
                            leadMigrateTracker.LeadsUpdatedCount = leads.Count();
                            leadMigrateTracker.LastModifiedOn = DateTime.UtcNow;
                            leadMigrateTracker.Status = UploadStatus.Completed;
                            await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        }
                        else
                        {
                            leadMigrateTracker.TotalUploadedCount = 0;
                            leadMigrateTracker.LastModifiedOn = DateTime.UtcNow;
                            leadMigrateTracker.Status = UploadStatus.Completed;
                            await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        }
                    }
                    else
                    {
                        List<Lead> duplicateLeads = new();
                        if (migrationType != LeadMigrationType.CreateDuplicateLead)
                        {
                            List<DuplicateDto> existingLeads = (await _dapperRepository.CheckDuplicateLeadsAsync(leads.Select(i => i.ContactNo).ToList() ?? new(), leads.Where(i => !string.IsNullOrEmpty(i.AlternateContactNo))?.Select(i => i.AlternateContactNo ?? string.Empty)?.ToList() ?? new(), input.TenantId)).ToList();
                            var existingContactNos = existingLeads.Where(i => !string.IsNullOrEmpty(i.ContactNo)).Select(i => i.ContactNo).ToList().Concat(existingLeads.Where(i => !string.IsNullOrEmpty(i.AlternateContactNo)).Select(i => i.AlternateContactNo).ToList());
                            duplicateLeads = leads.Where(i => existingContactNos.Contains(i.ContactNo) || existingContactNos.Contains(i.AlternateContactNo)).ToList();
                            leads.RemoveAll(i => existingContactNos.Contains(i.ContactNo) || existingContactNos.Contains(i.AlternateContactNo));

                            if (duplicateLeads?.Any() ?? false)
                            {
                                var duplicateLead = duplicateLeads
                                .Select(lead =>
                                {
                                    var user = masterItems.Users?.FirstOrDefault(i => i.Id == lead.AssignTo);
                                    var assignToUser = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == user?.UserName.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Duplicate Lead";
                                    invalidLead.AssignTo = assignToUser != null ? assignToUser.FirstName + " " + assignToUser.LastName : string.Empty;
                                    invalidLead.Source = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                                    invalidLead.SubSource = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                                    invalidLead.Created = lead.CreatedOn.Date;
                                    return invalidLead;
                                })
                                .ToList();
                                invalids.AddRange(duplicateLead);
                            }
                        }
                        else
                        {
                            duplicateLeads = null;
                        }

                        //update Tracker
                        leadMigrateTracker.Status = UploadStatus.InProgress;
                        leadMigrateTracker.TotalCount = dataTable.Rows.Count;
                        leadMigrateTracker.DistinctLeadCount = leads.Count() + results.Item2.Count();
                        leadMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        leadMigrateTracker.CreatedBy = input.CurrentUserId;
                        if (invalids.Any())
                        {
                            leadMigrateTracker.DuplicateCount = duplicateLeads?.Count() ?? 0;
                            leadMigrateTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty" || i.Errors == "Parent Lead Not Found" || i.Errors == "Child Lead Not Found" || i.Errors == "Invalid Source").Count();
                            byte[] bytes = CreateLeadHelper.CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = $"{input.TenantId}/Leads/Migrate";
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            //var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            leadMigrateTracker.InvalidDataS3BucketKey = key;
                        }
                        await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);

                        BulkMigrateBackgroundDto backgroundDto = new();
                        if (leads?.Count > 0)
                        {
                            int leadsPerchunk = leads.Count > 5000 ? 5000 : leads.Count;
                            var chunks = leads.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkMigrateBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = leadMigrateTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Leads = new(chunk),
                                    UserViews = masterItems.Users?.ToList(),
                                    HistoryUsers = masterItems.HistoryUsers,
                                    MigrationType = migrationType
                                };
                                await V2ExecuteDBOperationsAsync(backgroundDto, leadMigrateTracker, masterItems);
                                chunkIndex++;
                            }
                            leadMigrateTracker.TotalUploadedCount = leads.Count();
                            leadMigrateTracker.LastModifiedOn = DateTime.UtcNow;
                            leadMigrateTracker.Status = UploadStatus.Completed;
                            await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        }
                        else
                        {
                            leadMigrateTracker.TotalUploadedCount = 0;
                            leadMigrateTracker.LastModifiedOn = DateTime.UtcNow;
                            leadMigrateTracker.Status = UploadStatus.Completed;
                            await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(ex)}");
                    leadMigrateTracker.Status = UploadStatus.Failed;
                    leadMigrateTracker.Message = ex.Message;
                    leadMigrateTracker.LastModifiedBy = input.CurrentUserId;
                    leadMigrateTracker.CreatedBy = input.CurrentUserId;
                    await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                }
            }
            else
            {
                Console.WriteLine($"handler() -> tracker not found by the Id : {input.TrackerId}");
            }
        }
        private async Task V2ExecuteDBOperationsAsync(BulkMigrateBackgroundDto dto, LeadMigrateTracker? tracker, MasterItems masterItems)
        {
            if (!(dto.Leads?.Any() ?? false))
            {
                return;
            }
            try
            {
                var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
                if (dto.MigrationType == LeadMigrationType.CreateDuplicateLead)
                {
                    List<Lead> updateParentLeads = new();
                    var contactNos = dto.Leads.Select(i => i.ContactNo).Concat(dto.Leads.Select(i => i.AlternateContactNo)).Distinct().ToList();
                    var parentLeads = await _leadRepository.GetParentLeadByContactNosAsync(contactNos);
                    parentLeads.ToList().ForEach(parentLead =>
                    {
                        var filteredLeads = dto.Leads.Where(i => i.ContactNo.Contains(parentLead.ContactNo) || (!string.IsNullOrEmpty(parentLead.AlternateContactNo) && i.ContactNo.Contains(parentLead.AlternateContactNo)) ||
                                (!string.IsNullOrEmpty(i.AlternateContactNo) && i.AlternateContactNo.Contains(parentLead.ContactNo)) ||
                                (!string.IsNullOrEmpty(parentLead.AlternateContactNo) && !string.IsNullOrEmpty(i.AlternateContactNo) && i.AlternateContactNo.Contains(parentLead.AlternateContactNo))).ToList();

                        filteredLeads.ForEach(lead =>
                        {
                            lead.RootId = parentLead.Id;
                            lead.DuplicateLeadVersion = "D" + (parentLead.ChildLeadsCount + 1);
                            lead.ParentLeadId = parentLead.Id;
                        });
                        parentLead.ChildLeadsCount += 1;
                        updateParentLeads.Add(parentLead);
                    });
                    if (updateParentLeads?.Any() ?? false)
                    {
                        var conn = new NpgsqlConnection(connection);
                        conn.Open();
                        var query = QueryGenerator.GenerateLeadsUpdateQuery(DataBaseDetails.LRBSchema, "Leads", updateParentLeads);
                        await conn.ExecuteAsync(query, commandTimeout: 1000);
                        conn.Close();
                    }
                }
                var leads = await CreateLeadInDapperAsync(dto.Leads, connection, dto.TenantInfoDto?.Id ?? string.Empty, dto.CurrentUserId, masterItems);
                var addresses = V2ExtractAddressFromLeads(leads.Item1);
                if (addresses?.Any() ?? false)
                {
                    await V3AddBulkLocations(addresses, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                }
                var leadDtos = dto.Leads.Adapt<List<ViewLeadDto>>();
                dto.LeadDtos = leadDtos;
                List<LeadHistory> leadHistories = new();
                leadDtos.ForEach(leadDto =>
                {
                    leadDto.SetUsersInViewLeadDto(dto.HistoryUsers?.Adapt<List<UserDetailsDto>>() ?? new(), currentUserId: dto.CurrentUserId);
                    var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    leadHistory.Id = Guid.NewGuid();
                    leadHistory.CreatedDate = DateTime.UtcNow;
                    leadHistory.CreatedBy = dto.CurrentUserId;
                    leadHistory.IsDeleted = false;
                    leadHistories.Add(leadHistory);
                });
                var leadhistories = await CreateLeadHistoryInDapperAsync(leadHistories, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                await CreateLeadAssignmentHistory(leadDtos, dto.CurrentUserId, connection, dto.TenantInfoDto?.Id ?? string.Empty, CancellationToken.None);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Leads.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _leadMigrateTrackerRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync()",
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        private async Task V2UpdateDuplicateLeadsAsync(BulkMigrateBackgroundDto dto, MasterItems masterItems)
        {
            List<Lead> leadsToUpdate = new();
            LeadMigrateTracker? tracker = await _leadMigrateTrackerRepo.GetByIdAsync(dto.TrackerId);
            List<ViewLeadDto> oldLeads = new();
            try
            {
                if (dto.Leads?.Any() ?? false)
                {
                    if (tracker.MigrationType == LeadMigrationType.UpdateMissingInformation)
                    {
                        if (tracker.MigrationSubType == LeadMigrationSubType.ParentLead)
                        {
                            var parentLeads = await _leadRepository.GetParentLeadByContactNoAsync(dto.Leads.Select(i => i.ContactNo).ToList() ?? new());
                            parentLeads.ToList().ForEach(lead =>
                            {
                                var src = dto.Leads.FirstOrDefault(i => i.ContactNo == lead.ContactNo);
                                var updatedLeads = V2MappingMethodForUpdateMissingInfo(src ?? new(), lead, masterItems.Projects ?? new(), masterItems.Properties ?? new());
                                leadsToUpdate.Add(updatedLeads);
                            });
                        }
                        else if (tracker.MigrationSubType == LeadMigrationSubType.LatestChildLead)
                        {
                            var latestDuplicateLeads = await _leadRepository.GeLastDuplicateLeadsbyContactNosAsync(dto.Leads.Select(i => i.ContactNo).ToList() ?? new());
                            latestDuplicateLeads.ToList().ForEach(lead =>
                            {
                                var src = dto.Leads.FirstOrDefault(i => i.ContactNo == lead.ContactNo);
                                var updatedLeads = V2MappingMethodForUpdateMissingInfo(src ?? new(), lead, masterItems.Projects ?? new(), masterItems.Properties ?? new());
                                leadsToUpdate.Add(updatedLeads);
                            });
                        }
                        else
                        {
                            var duplicateLeadsToUpdates = await _leadRepository.GetLeadByNumberAsync(dto.Leads.Select(i => i.ContactNo).ToList() ?? new());
                            duplicateLeadsToUpdates.ToList().ForEach(lead =>
                            {
                                var src = dto.Leads.FirstOrDefault(i => i.ContactNo == lead.ContactNo);
                                var updatedLeads = V2MappingMethodForUpdateMissingInfo(src ?? new(), lead, masterItems.Projects ?? new(), masterItems.Properties ?? new());
                                leadsToUpdate.Add(updatedLeads);
                            });
                        }
                    }
                    if (tracker.MigrationType == LeadMigrationType.OverideExisitingLeadInformation)
                    {
                        if (tracker.MigrationSubType == LeadMigrationSubType.ParentLead)
                        {
                            var parentLeads = await _leadRepository.GetParentLeadByContactNoAsync(dto.Leads.Select(i => i.ContactNo).ToList() ?? new());
                            parentLeads.ToList().ForEach(lead =>
                            {
                                var src = dto.Leads.FirstOrDefault(i => i.ContactNo == lead.ContactNo);
                                var updatedLeads = V2MappingMethodForOverrideExistingLeads(src ?? new(), lead, masterItems.Projects ?? new(), masterItems.Properties ?? new());
                                leadsToUpdate.Add(updatedLeads);
                            });
                        }
                        else if (tracker.MigrationSubType == LeadMigrationSubType.LatestChildLead)
                        {
                            var latestDuplicateLeads = await _leadRepository.GeLastDuplicateLeadsbyContactNosAsync(dto.Leads.Select(i => i.ContactNo).ToList() ?? new());
                            latestDuplicateLeads.ToList().ForEach(lead =>
                            {
                                var src = dto.Leads.FirstOrDefault(i => i.ContactNo == lead.ContactNo);
                                var updatedLeads = V2MappingMethodForOverrideExistingLeads(src ?? new(), lead, masterItems.Projects ?? new(), masterItems.Properties ?? new());
                                leadsToUpdate.Add(updatedLeads);
                            });
                        }
                        else
                        {
                            var duplicateLeadsToUpdates = await _leadRepository.GetLeadByNumberAsync(dto.Leads.Select(i => i.ContactNo).ToList() ?? new());
                            duplicateLeadsToUpdates.ToList().ForEach(lead =>
                            {
                                var src = dto.Leads.FirstOrDefault(i => i.ContactNo == lead.ContactNo);
                                var updatedLeads = V2MappingMethodForOverrideExistingLeads(src ?? new(), lead, masterItems.Projects ?? new(), masterItems.Properties ?? new());
                                leadsToUpdate.Add(updatedLeads);
                            });
                        }
                    }
                }
                var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
                var leads = await UpdateLeadInDapperAsync(leadsToUpdate, connection, dto.TenantInfoDto?.Id ?? string.Empty, dto.CurrentUserId);
                if (leads.Item1?.Any() ?? false)
                {
                    var addresses = V2ExtractAddressFromLeads(leadsToUpdate);
                    if (addresses?.Any() ?? false)
                    {
                        await V3AddBulkLocations(addresses, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                    }
                    var leadDtos = leadsToUpdate.Adapt<List<ViewLeadDto>>();
                    List<LeadHistory> updateLeadHistories = new();
                    List<LeadHistory> insertLeadHistories = new();
                    var oldLeadHistorys = await _leadHistoryRepo.ListAsync(new LeadHistorySpec(leadDtos.Select(i => i.Id).ToList()));
                    foreach (var leadDto in leadDtos)
                    {
                        leadDto.SetUsersInViewLeadDto(dto.HistoryUsers?.Adapt<List<UserDetailsDto>>() ?? new(), currentUserId: dto.CurrentUserId);
                        var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                        var userId = leadDto.AssignTo ?? dto.CurrentUserId;
                        var oldLeadHistory = oldLeadHistorys.FirstOrDefault(i => i.LeadId == leadDto.Id && i.UserId == userId);
                        if (oldLeadHistory != null)
                        {
                            var leadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(oldLeadHistory, newLeadHistory);
                            updateLeadHistories.Add(leadHistory);
                        }
                        else
                        {
                            newLeadHistory.Id = Guid.NewGuid();
                            newLeadHistory.CreatedDate = DateTime.UtcNow;
                            newLeadHistory.CreatedBy = dto.CurrentUserId;
                            newLeadHistory.IsDeleted = false;
                            insertLeadHistories.Add(newLeadHistory);
                        }
                    }
                    if (updateLeadHistories?.Any() ?? false)
                    {
                        var leadhistories = await UpdateLeadHistoryInDapperAsync(updateLeadHistories, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                    }
                    if (insertLeadHistories?.Any() ?? false)
                    {
                        await CreateLeadHistoryInDapperAsync(insertLeadHistories, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                    }
                    if (tracker != null)
                    {
                        tracker.LeadsUpdatedCount += leads.Item1.Count();
                        tracker.LastModifiedBy = dto.CurrentUserId;
                        tracker.CreatedBy = dto.CurrentUserId;
                        await _leadMigrateTrackerRepo.UpdateAsync(tracker);
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                tracker.Status = UploadStatus.Failed;
                tracker.Message = e?.InnerException?.Message ?? e?.Message;
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _leadMigrateTrackerRepo.UpdateAsync(tracker);
            }
        }
        public Lead V2MappingMethodForUpdateMissingInfo(Lead src, Lead dest, List<Project> projects, List<Property> properties)
        {
            if (!dest.Enquiries.Any())
            {
                dest.Enquiries = new List<LeadEnquiry>();
            }
            try
            {
                if (src.Projects?.Any() ?? false)
                {
                    dest.Projects = new List<Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                }
                if (src.Properties?.Any() ?? false)
                {
                    dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                }
                if (dest.Email == null || dest.Email == string.Empty)
                { dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email; }
                if (dest.Notes == null) { dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes; }
                if (dest.ConfidentialNotes == null) { dest.ConfidentialNotes = string.IsNullOrEmpty(src.ConfidentialNotes) ? dest.ConfidentialNotes : src.ConfidentialNotes; }
                if (string.IsNullOrEmpty(dest.AlternateContactNo)) { dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo; }
                if (dest.AssignTo == null || dest.AssignTo == Guid.Empty) { dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo; }
                if (dest.AssignedFrom == null || dest.AssignedFrom == Guid.Empty) { dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom; }
                if (dest.Agencies == null || dest.Agencies.Count == 0) { dest.Agencies = src.Agencies ?? dest.Agencies; }
                if (dest.ChannelPartners == null || dest.ChannelPartners.Count == 0) { dest.ChannelPartners = src.ChannelPartners ?? dest.ChannelPartners; }
                if (dest.ReferralContactNo == null || dest.ReferralContactNo == string.Empty) { dest.ReferralContactNo = string.IsNullOrEmpty(src.ReferralContactNo) ? dest.ReferralContactNo : src.ReferralContactNo; }
                if (dest.ReferralName == null || dest.ReferralName == string.Empty) { dest.ReferralName = string.IsNullOrEmpty(src.ReferralName) ? dest.ReferralName : src.ReferralName; }
                if (dest.CreatedBy == null) { dest.CreatedBy = src.CreatedBy; }
                if (dest.Campaigns == null || dest.Campaigns.Count == 0) { dest.Campaigns = src.Campaigns ?? dest.Campaigns; }
                if (dest.ReferralEmail == null || dest.ReferralEmail == string.Empty)
                {
                    dest.ReferralEmail = string.IsNullOrEmpty(src.ReferralEmail) ? dest.ReferralEmail : src.ReferralEmail;
                }
                if (dest.CustomFlags == null || dest.CustomFlags.Count == 0)
                {
                    dest.CustomFlags = src.CustomFlags;
                }
                if (dest.LastModifiedBy == null) { dest.LastModifiedBy = src.LastModifiedBy; }
                if (string.IsNullOrEmpty(dest.Notes))
                {
                    dest.Notes = src.Notes;
                }
                if (dest.ScheduledDate == null) { dest.ScheduledDate = src.ScheduledDate; }
                if (dest.CreatedOn == null) { dest.CreatedOn = src.CreatedOn; }
                if (dest.CustomLeadStatus == null) { dest.CustomLeadStatus = src.CustomLeadStatus; }
                if (dest.Enquiries.FirstOrDefault().PropertyType == null)
                {
                    dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != default ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                }
                if (dest.Enquiries.FirstOrDefault().BHKs == null || dest.Enquiries.FirstOrDefault().BHKs.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                }
                if (dest.Enquiries.FirstOrDefault().Floors == null || dest.Enquiries.FirstOrDefault().Floors.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().Floors = src.Enquiries?.FirstOrDefault()?.Floors != default ? src.Enquiries?.FirstOrDefault()?.Floors : dest.Enquiries[0].Floors;
                }
                if (dest.Enquiries.FirstOrDefault().Beds == null || dest.Enquiries.FirstOrDefault().Beds.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().Beds = src.Enquiries?.FirstOrDefault()?.Beds != default ? src.Enquiries?.FirstOrDefault()?.Beds : dest.Enquiries[0].Beds;
                }
                if (dest.Enquiries.FirstOrDefault().Baths == null || dest.Enquiries.FirstOrDefault().Baths.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().Baths = src.Enquiries?.FirstOrDefault()?.Baths != default ? src.Enquiries?.FirstOrDefault()?.Baths : dest.Enquiries[0].Baths;
                }
                if (dest.Enquiries.FirstOrDefault().EnquiryTypes == null || dest.Enquiries.FirstOrDefault().EnquiryTypes.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes;
                }
                if (dest.Enquiries.FirstOrDefault().SaleType == null)
                {
                    dest.Enquiries.FirstOrDefault().SaleType = dest.Enquiries[0].SaleType;
                }
                if (dest.Enquiries.FirstOrDefault().OfferType == null || dest.Enquiries.FirstOrDefault().OfferType == OfferType.None)
                {
                    dest.Enquiries.FirstOrDefault().OfferType = src.Enquiries?.FirstOrDefault()?.OfferType != default ? src.Enquiries?.FirstOrDefault()?.OfferType : dest.Enquiries[0].OfferType;
                }
                if (dest.Enquiries.FirstOrDefault().Furnished == null || dest.Enquiries.FirstOrDefault().Furnished == FurnishStatus.Unknown)
                {
                    dest.Enquiries.FirstOrDefault().Furnished = src.Enquiries?.FirstOrDefault()?.Furnished != default ? src.Enquiries?.FirstOrDefault()?.Furnished : dest.Enquiries[0].Furnished;
                }
                if (string.IsNullOrEmpty(dest.Enquiries.FirstOrDefault().SubSource))
                {
                    dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource;
                }
                if (dest.Enquiries.FirstOrDefault().LeadSource == null)
                {
                    dest.Enquiries.FirstOrDefault().LeadSource = src.Enquiries?.FirstOrDefault()?.LeadSource != default ? src.Enquiries.FirstOrDefault().LeadSource : dest.Enquiries[0].LeadSource;
                }
                if (dest.Enquiries.FirstOrDefault().LowerBudget == null || dest.Enquiries.FirstOrDefault().LowerBudget == 0)
                {
                    dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                }
                if (dest.Enquiries.FirstOrDefault().UpperBudget == null || dest.Enquiries.FirstOrDefault().UpperBudget == 0)
                {
                    dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                }
                if (dest.Enquiries.FirstOrDefault().CarpetArea == null || dest.Enquiries.FirstOrDefault().CarpetArea == 0)
                {
                    dest.Enquiries.FirstOrDefault().CarpetArea = (src.Enquiries?.FirstOrDefault()?.CarpetArea != default && src.Enquiries?.FirstOrDefault()?.CarpetArea != null) ? src.Enquiries?.FirstOrDefault()?.CarpetArea : dest.Enquiries[0].CarpetArea;
                }
                if (dest.Enquiries.FirstOrDefault().CarpetAreaUnitId == Guid.Empty || dest.Enquiries.FirstOrDefault().CarpetAreaUnitId == default)
                {
                    dest.Enquiries.FirstOrDefault().CarpetAreaUnitId = src.Enquiries.FirstOrDefault().CarpetAreaUnitId != default ? src.Enquiries.FirstOrDefault().CarpetAreaUnitId : dest.Enquiries[0].CarpetAreaUnitId;
                }
                if (string.IsNullOrWhiteSpace(dest.Enquiries?.FirstOrDefault()?.Currency))
                {
                    dest.Enquiries.FirstOrDefault().Currency = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Currency) ? dest.Enquiries[0].Currency : src.Enquiries?.FirstOrDefault()?.Currency;
                }
                if (dest.Enquiries.FirstOrDefault().BHKTypes == null || dest.Enquiries.FirstOrDefault().BHKTypes.Count == 0)
                {
                    dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes;
                }
                if (dest.Enquiries?.FirstOrDefault()?.Purpose == null || dest?.Enquiries?.FirstOrDefault()?.Purpose == Purpose.None)
                {
                    dest.Enquiries.FirstOrDefault().Purpose = src.Enquiries?.FirstOrDefault()?.Purpose != default ? src.Enquiries?.FirstOrDefault()?.Purpose : dest.Enquiries[0].Purpose;
                }
                var address = dest.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault();
                if (address == null)
                {
                    address = new Address();
                    address.Id = Guid.Empty;
                    dest.Enquiries.FirstOrDefault()?.Addresses?.Add(address);
                }
                if (string.IsNullOrEmpty(address?.SubLocality))
                {
                    address.SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality)
                        ? address.SubLocality
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality;
                }
                if (string.IsNullOrEmpty(address?.City))
                {
                    address.City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City)
                        ? address.City
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                }

                if (string.IsNullOrEmpty(address?.State))
                {
                    address.State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State)
                        ? address.State
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                }
                if (string.IsNullOrEmpty(address?.Country))
                {
                    address.Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country)
                        ? address.Country
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                }
                if (string.IsNullOrEmpty(address?.SubCommunity))
                {
                    address.SubCommunity = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubCommunity)
                        ? address.SubCommunity
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubCommunity;
                }
                if (string.IsNullOrEmpty(address?.Community))
                {
                    address.Community = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Community)
                        ? address.Community
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Community;
                }
                if (string.IsNullOrEmpty(address?.TowerName))
                {
                    address.TowerName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.TowerName)
                        ? address.TowerName
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.TowerName;
                }
                if (string.IsNullOrEmpty(address?.PostalCode))
                {
                    address.PostalCode = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.PostalCode)
                        ? address.PostalCode
                        : src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.PostalCode;
                }
                if (dest.Appointments == null || dest.Appointments.Count == 0)
                {
                    dest.Appointments = new List<LeadAppointment>();
                }
                if (src.Appointments?.Any() ?? false)
                {
                    if (dest.Appointments == null)
                    {
                        dest.Appointments = new List<LeadAppointment>();
                    }
                    dest.Appointments = src.Appointments;
                }
                dest.ChannelPartnerName = string.IsNullOrEmpty(src.ChannelPartnerName) ? dest.ChannelPartnerName : src.ChannelPartnerName;
                dest.ChannelPartnerExecutiveName = string.IsNullOrEmpty(src.ChannelPartnerExecutiveName) ? dest.ChannelPartnerExecutiveName : src.ChannelPartnerExecutiveName;
                dest.ChannelPartnerContactNo = string.IsNullOrEmpty(src.ChannelPartnerContactNo) ? dest.ChannelPartnerContactNo : src.ChannelPartnerContactNo;
                dest.UploadType = src?.UploadType ?? dest.UploadType;
                dest.UploadTypeName = string.IsNullOrEmpty(src.UploadTypeName) ? dest.UploadTypeName : src.UploadTypeName;
                if (dest.Enquiries.FirstOrDefault().NetArea == null || dest.Enquiries.FirstOrDefault().NetArea == 0)
                {
                    dest.Enquiries.FirstOrDefault().NetArea = (src.Enquiries?.FirstOrDefault()?.NetArea != default && src.Enquiries?.FirstOrDefault()?.NetArea != null) ? src.Enquiries?.FirstOrDefault()?.NetArea : dest.Enquiries[0].NetArea;
                }
                if (dest.Enquiries.FirstOrDefault().NetAreaUnitId == Guid.Empty || dest.Enquiries.FirstOrDefault().NetAreaUnitId == default)
                {
                    dest.Enquiries.FirstOrDefault().NetAreaUnitId = src.Enquiries.FirstOrDefault().NetAreaUnitId != default ? src.Enquiries.FirstOrDefault().NetAreaUnitId : dest.Enquiries[0].NetAreaUnitId;
                }
                if (dest.Enquiries.FirstOrDefault().BuiltUpArea == null || dest.Enquiries.FirstOrDefault().BuiltUpArea == 0)
                {
                    dest.Enquiries.FirstOrDefault().BuiltUpArea = (src.Enquiries?.FirstOrDefault()?.BuiltUpArea != default && src.Enquiries?.FirstOrDefault()?.BuiltUpArea != null) ? src.Enquiries?.FirstOrDefault()?.BuiltUpArea : dest.Enquiries[0].BuiltUpArea;
                }
                if (dest.Enquiries.FirstOrDefault().BuiltUpAreaUnitId == Guid.Empty || dest.Enquiries.FirstOrDefault().BuiltUpAreaUnitId == default)
                {
                    dest.Enquiries.FirstOrDefault().BuiltUpAreaUnitId = src.Enquiries.FirstOrDefault().BuiltUpAreaUnitId != default ? src.Enquiries.FirstOrDefault().BuiltUpAreaUnitId : dest.Enquiries[0].BuiltUpAreaUnitId;
                }
                if (dest.Enquiries.FirstOrDefault().PropertyArea == null || dest.Enquiries.FirstOrDefault().PropertyArea == 0)
                {
                    dest.Enquiries.FirstOrDefault().PropertyArea = (src.Enquiries?.FirstOrDefault()?.PropertyArea != default && src.Enquiries?.FirstOrDefault()?.PropertyArea != null) ? src.Enquiries?.FirstOrDefault()?.PropertyArea : dest.Enquiries[0].PropertyArea;
                }
                if (dest.Enquiries.FirstOrDefault().PropertyAreaUnitId == Guid.Empty || dest.Enquiries.FirstOrDefault().PropertyAreaUnitId == default)
                {
                    dest.Enquiries.FirstOrDefault().PropertyAreaUnitId = src.Enquiries.FirstOrDefault().PropertyAreaUnitId != default ? src.Enquiries.FirstOrDefault().PropertyAreaUnitId : dest.Enquiries[0].PropertyAreaUnitId;
                }
                if (dest.Enquiries.FirstOrDefault().SaleableArea == null || dest.Enquiries.FirstOrDefault().SaleableArea == 0)
                {
                    dest.Enquiries.FirstOrDefault().SaleableArea = (src.Enquiries?.FirstOrDefault()?.SaleableArea != default && src.Enquiries?.FirstOrDefault()?.SaleableArea != null) ? src.Enquiries?.FirstOrDefault()?.SaleableArea : dest.Enquiries[0].SaleableArea;
                }
                if (dest.Enquiries.FirstOrDefault().SaleableAreaUnitId == Guid.Empty || dest.Enquiries.FirstOrDefault().SaleableAreaUnitId == default)
                {
                    dest.Enquiries.FirstOrDefault().SaleableAreaUnitId = src.Enquiries.FirstOrDefault().SaleableAreaUnitId != default ? src.Enquiries.FirstOrDefault().SaleableAreaUnitId : dest.Enquiries[0].SaleableAreaUnitId;
                }
                if (string.IsNullOrEmpty(dest.Enquiries.FirstOrDefault().UnitName))
                {
                    dest.Enquiries.FirstOrDefault().UnitName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.UnitName) ? dest.Enquiries[0].UnitName : src.Enquiries?.FirstOrDefault()?.UnitName;
                }
                if (string.IsNullOrEmpty(dest.Enquiries.FirstOrDefault().ClusterName))
                {
                    dest.Enquiries.FirstOrDefault().ClusterName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.ClusterName) ? dest.Enquiries[0].ClusterName : src.Enquiries?.FirstOrDefault()?.ClusterName;
                }
                if (dest.Nationality == null || dest.Nationality == string.Empty)
                {
                    dest.Nationality = string.IsNullOrEmpty(src.Nationality) ? dest.Nationality : src.Nationality;
                }
                var customeraddress = dest.Address;
                if (customeraddress == null)
                {
                    customeraddress = new Address();
                    customeraddress.Id = Guid.Empty;
                }
                if (string.IsNullOrEmpty(customeraddress?.SubLocality))
                {
                    customeraddress.SubLocality = string.IsNullOrEmpty(src?.Address?.SubLocality)? customeraddress.SubLocality: src.Address?.SubLocality;
                }
                if (string.IsNullOrEmpty(customeraddress?.City))
                {
                    customeraddress.City = string.IsNullOrEmpty(src?.Address?.City) ? customeraddress.City : src.Address?.City;
                }
                if (string.IsNullOrEmpty(customeraddress?.State))
                {
                    customeraddress.State = string.IsNullOrEmpty(src?.Address?.State) ? customeraddress.State : src.Address?.State;

                }
                if (string.IsNullOrEmpty(customeraddress?.Country))
                {
                    customeraddress.Country = string.IsNullOrEmpty(src?.Address?.Country) ? customeraddress.Country : src.Address?.Country;

                }
                if (string.IsNullOrEmpty(customeraddress?.SubCommunity))
                {
                    customeraddress.SubCommunity = string.IsNullOrEmpty(src?.Address?.SubCommunity) ? customeraddress.SubCommunity : src.Address?.SubCommunity;

                }
                if (string.IsNullOrEmpty(customeraddress?.Community))
                {
                    customeraddress.Community = string.IsNullOrEmpty(src?.Address?.Community) ? customeraddress.Community : src.Address?.Community;

                }
                if (string.IsNullOrEmpty(customeraddress?.TowerName))
                {
                    customeraddress.TowerName = string.IsNullOrEmpty(src?.Address?.TowerName) ? customeraddress.TowerName : src.Address?.TowerName;

                }
                if (string.IsNullOrEmpty(customeraddress?.PostalCode))
                {
                    customeraddress.PostalCode = string.IsNullOrEmpty(src?.Address?.PostalCode) ? customeraddress.PostalCode : src.Address?.PostalCode;

                }
                if (dest.ClosingManager == null || dest.ClosingManager == Guid.Empty) { dest.ClosingManager = (src.ClosingManager != default && src.ClosingManager != null) ? src.ClosingManager : dest.ClosingManager; }
                if (dest.SourcingManager == null || dest.SourcingManager == Guid.Empty) { dest.SourcingManager = (src.SourcingManager != default && src.SourcingManager != null) ? src.SourcingManager : dest.SourcingManager; }
                if (dest?.Profession == null)
                {
                    dest.Profession = src.Profession != default ? src.Profession : dest.Profession;
                }
                if (dest.CompanyName == null || dest.CompanyName == string.Empty)
                {
                    dest.CompanyName = string.IsNullOrEmpty(src.CompanyName) ? dest.CompanyName : src.CompanyName;
                }
                if (dest?.PossesionType == null || dest?.PossesionType == PossesionType.None)
                {
                    dest.PossesionType = src.PossesionType != default ? src?.PossesionType : dest.PossesionType;
                }
                dest.LandLine = string.IsNullOrEmpty(src.LandLine) ? dest.LandLine : src.LandLine;

                if (dest?.Gender == null || dest.Gender == Gender.NotMentioned)
                {
                    dest.Gender = src.Gender != default ? src.Gender : dest.Gender;
                }
                if (dest?.MaritalStatus == null || dest.MaritalStatus == MaritalStatusType.NotMentioned)
                {
                    dest.MaritalStatus = src.MaritalStatus != default ? src.MaritalStatus : dest.MaritalStatus;
                }
                if (dest.DateOfBirth == null)
                {
                    dest.DateOfBirth = src.DateOfBirth;
                }
                return dest;
            }
            catch
            {
                return dest;
            }
        }
        public Lead V2MappingMethodForOverrideExistingLeads(Lead src, Lead dest, List<Project> projects, List<Property> properties)
        {
            if (!dest.Enquiries.Any())
            {
                dest.Enquiries = new List<LeadEnquiry>();
            }
            try
            {
                if (src.Projects?.Any() ?? false)
                {
                    dest.Projects = new List<Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                }
                if (src.Properties?.Any() ?? false)
                {
                    dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                }
                dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email;
                dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes;
                dest.ConfidentialNotes = string.IsNullOrEmpty(src.ConfidentialNotes) ? dest.ConfidentialNotes : src.ConfidentialNotes;
                dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo;
                dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo;
                dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom;
                dest.Agencies = src.Agencies ?? dest.Agencies;
                dest.ReferralContactNo = string.IsNullOrEmpty(src.ReferralContactNo) ? dest.ReferralContactNo : src.ReferralContactNo;
                dest.ReferralName = string.IsNullOrEmpty(src.ReferralName) ? dest.ReferralName : src.ReferralName;
                dest.CreatedBy = (src.CreatedBy == null || src.CreatedBy == Guid.Empty) ? dest.CreatedBy : src.CreatedBy;
                dest.CustomFlags = (src.CustomFlags == null || src.CustomFlags.Count == 0) ? dest.CustomFlags : src.CustomFlags;
                dest.LastModifiedBy = src.LastModifiedBy;
                dest.LastModifiedOn = DateTime.UtcNow;
                dest.Campaigns = src.Campaigns ?? dest.Campaigns;
                dest.CreatedOn = (src.CreatedOn == null) ? dest.CreatedOn : src.CreatedOn;
                dest.ReferralEmail = string.IsNullOrEmpty(src.ReferralEmail) ? dest.ReferralEmail : src.ReferralEmail;
                dest.Notes = (string.IsNullOrEmpty(src.Notes)) ? dest.Notes : src.Notes;
                dest.ScheduledDate = (src.ScheduledDate == null) ? dest.ScheduledDate : src.ScheduledDate;
                dest.CustomLeadStatus = src.CustomLeadStatus ?? dest.CustomLeadStatus;
                dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != default ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                dest.Enquiries.FirstOrDefault().NoOfBHKs = src.Enquiries?.FirstOrDefault()?.NoOfBHKs != default ? src.Enquiries.FirstOrDefault().NoOfBHKs : dest.Enquiries[0].NoOfBHKs;
                dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                dest.Enquiries.FirstOrDefault().Beds = src.Enquiries?.FirstOrDefault()?.Beds != default ? src.Enquiries?.FirstOrDefault()?.Beds : dest.Enquiries[0].Beds;
                dest.Enquiries.FirstOrDefault().Floors = src.Enquiries?.FirstOrDefault()?.Floors != default ? src.Enquiries?.FirstOrDefault()?.Floors : dest.Enquiries[0].Floors;
                dest.Enquiries.FirstOrDefault().Baths = src.Enquiries?.FirstOrDefault()?.Baths != default ? src.Enquiries?.FirstOrDefault()?.Baths : dest.Enquiries[0].Baths;
                dest.Enquiries.FirstOrDefault().EnquiredFor = src.Enquiries?.FirstOrDefault()?.EnquiredFor != default ? src.Enquiries.FirstOrDefault().EnquiredFor : dest.Enquiries[0].EnquiredFor;
                dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes;
                dest.Enquiries.FirstOrDefault().SaleType = src.Enquiries?.FirstOrDefault()?.SaleType != default ? src.Enquiries?.FirstOrDefault()?.SaleType : dest.Enquiries[0]?.SaleType;
                dest.Enquiries.FirstOrDefault().OfferType = src.Enquiries?.FirstOrDefault()?.OfferType != default ? src.Enquiries?.FirstOrDefault()?.OfferType : dest.Enquiries[0]?.OfferType;
                dest.Enquiries.FirstOrDefault().Furnished = src.Enquiries?.FirstOrDefault()?.Furnished != default ? src.Enquiries?.FirstOrDefault()?.Furnished : dest.Enquiries[0]?.Furnished;
                dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource;
                dest.Enquiries.FirstOrDefault().LeadSource = src.Enquiries?.FirstOrDefault()?.LeadSource != default ? src.Enquiries.FirstOrDefault().LeadSource : dest.Enquiries[0].LeadSource;
                dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                dest.Enquiries.FirstOrDefault().CarpetArea = (src.Enquiries?.FirstOrDefault()?.CarpetArea != default && src.Enquiries?.FirstOrDefault()?.CarpetArea != null) ? src.Enquiries?.FirstOrDefault()?.CarpetArea : dest.Enquiries[0].CarpetArea;
                dest.Enquiries.FirstOrDefault().CarpetAreaUnitId = (src.Enquiries?.FirstOrDefault()?.CarpetAreaUnitId != default) ? src.Enquiries.FirstOrDefault().CarpetAreaUnitId : dest.Enquiries.FirstOrDefault().CarpetAreaUnitId;
                dest.Enquiries.FirstOrDefault().Currency = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Currency) ? dest.Enquiries[0].Currency : src.Enquiries?.FirstOrDefault()?.Currency;
                dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubLocality : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.City : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.State : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Country : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubCommunity = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubCommunity) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubCommunity : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubCommunity;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Community = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Community) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Community : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Community;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().TowerName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.TowerName) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.TowerName : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.TowerName;
                dest.Enquiries.FirstOrDefault().Purpose = src.Enquiries?.FirstOrDefault()?.Purpose != default ? src.Enquiries?.FirstOrDefault()?.Purpose : dest.Enquiries[0]?.Purpose;

                if (src.Appointments?.Any() ?? false)
                {
                    if (dest.Appointments == null)
                    {
                        dest.Appointments = new List<LeadAppointment>();
                    }
                    dest.Appointments = src.Appointments;
                }
                dest.ChannelPartnerName = string.IsNullOrEmpty(src.ChannelPartnerName) ? dest.ChannelPartnerName : src.ChannelPartnerName;
                dest.ChannelPartnerExecutiveName = string.IsNullOrEmpty(src.ChannelPartnerExecutiveName) ? dest.ChannelPartnerExecutiveName : src.ChannelPartnerExecutiveName;
                dest.ChannelPartnerContactNo = string.IsNullOrEmpty(src.ChannelPartnerContactNo) ? dest.ChannelPartnerContactNo : src.ChannelPartnerContactNo;
                dest.ChannelPartners = src.ChannelPartners ?? dest.ChannelPartners;
                dest.UploadType = src?.UploadType ?? dest.UploadType;
                dest.UploadTypeName = string.IsNullOrEmpty(src.UploadTypeName) ? dest.UploadTypeName : src.UploadTypeName;
                dest.Enquiries.FirstOrDefault().NetArea = (src.Enquiries?.FirstOrDefault()?.NetArea != default && src.Enquiries?.FirstOrDefault()?.NetArea != null) ? src.Enquiries?.FirstOrDefault()?.NetArea : dest.Enquiries[0].NetArea;
                dest.Enquiries.FirstOrDefault().NetAreaUnitId = (src.Enquiries?.FirstOrDefault()?.NetAreaUnitId != default) ? src.Enquiries.FirstOrDefault().NetAreaUnitId : dest.Enquiries.FirstOrDefault().NetAreaUnitId;
                dest.Enquiries.FirstOrDefault().PropertyArea = (src.Enquiries?.FirstOrDefault()?.PropertyArea != default && src.Enquiries?.FirstOrDefault()?.PropertyArea != null) ? src.Enquiries?.FirstOrDefault()?.PropertyArea : dest.Enquiries[0].PropertyArea;
                dest.Enquiries.FirstOrDefault().PropertyAreaUnitId = (src.Enquiries?.FirstOrDefault()?.PropertyAreaUnitId != default) ? src.Enquiries.FirstOrDefault().PropertyAreaUnitId : dest.Enquiries.FirstOrDefault().PropertyAreaUnitId;
                dest.Enquiries.FirstOrDefault().BuiltUpArea = (src.Enquiries?.FirstOrDefault()?.BuiltUpArea != default && src.Enquiries?.FirstOrDefault()?.BuiltUpArea != null) ? src.Enquiries?.FirstOrDefault()?.BuiltUpArea : dest.Enquiries[0].BuiltUpArea;
                dest.Enquiries.FirstOrDefault().BuiltUpAreaUnitId = (src.Enquiries?.FirstOrDefault()?.BuiltUpAreaUnitId != default) ? src.Enquiries.FirstOrDefault().BuiltUpAreaUnitId : dest.Enquiries.FirstOrDefault().BuiltUpAreaUnitId;
                dest.Enquiries.FirstOrDefault().SaleableArea = (src.Enquiries?.FirstOrDefault()?.SaleableArea != default && src.Enquiries?.FirstOrDefault()?.SaleableArea != null) ? src.Enquiries?.FirstOrDefault()?.SaleableArea : dest.Enquiries[0].SaleableArea;
                dest.Enquiries.FirstOrDefault().SaleableAreaUnitId = (src.Enquiries?.FirstOrDefault()?.SaleableAreaUnitId != default) ? src.Enquiries.FirstOrDefault().SaleableAreaUnitId : dest.Enquiries.FirstOrDefault().SaleableAreaUnitId;
                dest.Enquiries.FirstOrDefault().UnitName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.UnitName) ? dest.Enquiries[0].UnitName : src.Enquiries?.FirstOrDefault()?.UnitName;
                dest.Enquiries.FirstOrDefault().ClusterName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.ClusterName) ? dest.Enquiries[0].ClusterName : src.Enquiries?.FirstOrDefault()?.ClusterName;
                dest.Nationality = string.IsNullOrEmpty(src.Nationality) ? dest.Nationality : src.Nationality;
                dest.Address.SubLocality = string.IsNullOrEmpty(src.Address?.SubLocality) ? dest.Address?.SubLocality : src.Address?.SubLocality;
                dest.Address.City = string.IsNullOrEmpty(src.Address?.City) ? dest.Address?.City : src.Address?.City;
                dest.Address.State = string.IsNullOrEmpty(src.Address?.State) ? dest.Address?.State : src.Address?.State;
                dest.Address.Country = string.IsNullOrEmpty(src.Address?.Country) ? dest.Address?.Country : src.Address?.Country;
                dest.Address.SubCommunity = string.IsNullOrEmpty(src.Address?.SubCommunity) ? dest.Address?.SubCommunity : src.Address?.SubCommunity;
                dest.Address.Community = string.IsNullOrEmpty(src.Address?.Community) ? dest.Address?.SubLocality : src.Address?.Community;
                dest.Address.TowerName = string.IsNullOrEmpty(src.Address?.TowerName) ? dest.Address?.TowerName : src.Address?.TowerName;
                dest.Address.PostalCode = string.IsNullOrEmpty(src.Address?.PostalCode) ? dest.Address?.PostalCode : src.Address?.PostalCode;
                dest.ClosingManager = (src.ClosingManager != default && src.ClosingManager != null) ? src.ClosingManager : dest.ClosingManager;
                dest.SourcingManager = (src.SourcingManager != default && src.SourcingManager != null) ? src.SourcingManager : dest.SourcingManager;
                dest.Profession = src.Profession != default ? src.Profession : dest.Profession;
                dest.PossesionType = src?.PossesionType != default ? src?.PossesionType : dest?.PossesionType;
                dest.LandLine = string.IsNullOrEmpty(src.LandLine) ? dest.LandLine : src.LandLine;

                dest.Gender = src.Gender != default ? src.Gender : dest.Gender;
                dest.MaritalStatus = src.MaritalStatus != default ? src.MaritalStatus : dest.MaritalStatus;
                dest.DateOfBirth = (src.DateOfBirth == null) ? dest.DateOfBirth : src.DateOfBirth;
                return dest;
            }
            catch
            {
                return dest;
            }
        }
        public async Task<(List<Lead>?, bool)> UpdateLeadInDapperAsync(List<Lead> leads, string connectionString, string tenantId, Guid currentUserId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    //leads
                    var addLeads = leads.Select(i =>
                    {
                        var leadDto = i.Adapt<LeadDapperDto>();
                        leadDto.CustomLeadStatusId = i.CustomLeadStatus?.Id ?? Guid.Empty;

                        return leadDto;
                    }).ToList();

                    var properties = GetMappedProperties<LeadDapperDto>();
                    var insertQuery = QueryGenerator.V2GenerateUpdateQuery(tenantId, DataBaseDetails.LRBSchema, "Leads", properties, addLeads);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    //enquiries
                    var enquiries = leads
                       .Where(lead => lead.Enquiries != null && lead.Enquiries.Any())
                       .SelectMany(lead =>
                            lead.Enquiries.Select(enquiry =>
                            {
                                var leadEnquiryDto = enquiry.Adapt<LeadEnquiryDapperDto>();
                                return leadEnquiryDto;
                            })
                     ).ToList();
                    if (enquiries.Any())
                    {
                        var enquiryProperties = GetMappedProperties<LeadEnquiryDapperDto>();
                        var enquiryInsertQuery = QueryGenerator.V2GenerateUpdateQuery(null, DataBaseDetails.LRBSchema, "LeadEnquiries", enquiryProperties, enquiries);
                        await conn.ExecuteAsync(enquiryInsertQuery, commandTimeout: 1000);
                    }

                    //addresses
                    var addresses = leads.Select(i => i.Enquiries.FirstOrDefault())
                        .Where(i => i.Addresses != null && i.Addresses.Any()).SelectMany(address => address.Addresses).Where(address => address.Id != Guid.Empty).Adapt<List<AddressDapperDto>>();

                    if (addresses.Any())
                    {
                        var addressProperties = GetMappedProperties<AddressDapperDto>();
                        var addressInsertQuery = QueryGenerator.V2GenerateUpdateQuery(null, DataBaseDetails.LRBSchema, "Addresses", addressProperties, addresses);
                        await conn.ExecuteAsync(addressInsertQuery, commandTimeout: 1000);
                    }

                    var newAddresses = leads.Select(i => i.Enquiries.FirstOrDefault())
                        .Where(i => i.Addresses != null && i.Addresses.Any()).SelectMany(address => address.Addresses).Where(address => address.Id == Guid.Empty).ToList();

                    newAddresses.ForEach(address => address.Id = Guid.NewGuid()); // Assign new GUIDs
                    var addressDtos = newAddresses.Adapt<List<AddressDapperDto>>();

                    if (addressDtos.Any())
                    {
                        var addressProperties = GetMappedProperties<Lrb.Application.Lead.Web.Dtos.AddressDapperDto>();
                        var addressInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "Addresses", addressProperties, addressDtos);
                        await conn.ExecuteAsync(addressInsertQuery, commandTimeout: 1000);
                    }

                    // enquiry address
                    var enquiryAddresses = leads.Select(i => i.Enquiries.FirstOrDefault())
                    .Where(i => i.Addresses != null && i.Addresses.Any())
                    .SelectMany(en =>
                        en.Addresses.Select(address => new AddressLeadEnquiryDto
                        {
                            EnquiriesId = en.Id,
                            AddressesId = address.Id
                        })
                    ).ToList();
                    if (enquiryAddresses.Any())
                    {
                        var enquiryAddressProperties = GetMappedProperties<AddressLeadEnquiryDto>();
                        var enquiryAddressInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "AddressLeadEnquiry", enquiryAddressProperties, "EnquiriesId", "AddressesId", enquiryAddresses);
                        await conn.ExecuteAsync(enquiryAddressInsertQuery, commandTimeout: 1000);
                    }

                    //projects
                    var leadProjects = leads
                    .Where(lead => lead.Projects != null && lead.Projects.Any())
                    .SelectMany(lead => lead.Projects.Select(project => new LeadProjectDTO
                    {
                        LeadsId = lead.Id,
                        ProjectsId = project.Id
                    })).ToList();
                    if (leadProjects.Any())
                    {
                        List<string> columnNames = new List<string> { "LeadsId", "ProjectsId" };
                        var projectInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "LeadProject", columnNames, "LeadsId", "ProjectsId", leadProjects);
                        await conn.ExecuteAsync(projectInsertQuery, commandTimeout: 1000);
                    }

                    //properties
                    var leadProperties = leads
                    .Where(lead => lead.Properties != null && lead.Properties.Any())
                    .SelectMany(lead => lead.Properties.Select(i => new LeadPropertyDto
                    {
                        LeadsId = lead.Id,
                        PropertiesId = i.Id
                    })).ToList();
                    if (leadProperties.Any())
                    {
                        List<string> columnNames = new List<string> { "LeadsId", "PropertiesId" };
                        var propertyInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "LeadProperty", columnNames, "LeadsId", "PropertiesId", leadProperties);
                        await conn.ExecuteAsync(propertyInsertQuery, commandTimeout: 1000);
                    }

                    //agencies
                    var leadAgencies = leads
                    .Where(lead => lead.Agencies != null && lead.Agencies.Any())
                    .SelectMany(lead => lead.Agencies.Select(i => new AgencyLeadDto
                    {
                        LeadsId = lead.Id,
                        AgenciesId = i.Id
                    })).ToList();
                    if (leadAgencies.Any())
                    {
                        List<string> columnNames = new List<string> { "AgenciesId", "LeadsId" };
                        var agencyInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "AgencyLead", columnNames, "LeadsId", "AgenciesId", leadAgencies);
                        await conn.ExecuteAsync(agencyInsertQuery, commandTimeout: 1000);
                    }

                    //channel partners
                    var leadChannelpartners = leads
                    .Where(lead => lead.ChannelPartners != null && lead.ChannelPartners.Any())
                    .SelectMany(lead => lead.ChannelPartners.Select(i => new ChannelPartnerLeadDto
                    {
                        LeadsId = lead.Id,
                        ChannelPartnersId = i.Id
                    })).ToList();
                    if (leadChannelpartners.Any())
                    {
                        List<string> columnNames = new List<string> { "ChannelPartnersId", "LeadsId" };
                        var channelpartnersInsertQuery = QueryGenerator.GenerateDynamicUpdateQueryForManyToMany(DataBaseDetails.LRBSchema, "ChannelPartnerLead", columnNames, "LeadsId", "ChannelPartnersId", leadChannelpartners);
                        await conn.ExecuteAsync(channelpartnersInsertQuery, commandTimeout: 1000);
                    }

                    // appointments
                    var appointments = leads
                             .Where(lead => lead.Appointments != null && lead.Appointments.Any())
                             .SelectMany(lead =>
                             lead.Appointments.Select(i =>
                             {
                                 var leadAppointmentsDto = i.Adapt<LeadAppointmentDapperDto>();
                                 leadAppointmentsDto.Id = Guid.NewGuid();
                                 leadAppointmentsDto.LeadId = lead.Id;
                                 return leadAppointmentsDto;
                             })
                             ).ToList();
                    if (appointments.Any())
                    {
                        var appointmentUpdateQuery = QueryGenerator.GenerateDeleteByLeadIdsQuery(DataBaseDetails.LRBSchema, "LeadAppointments", leads.Select(i => i.Id).ToList());
                        await conn.ExecuteAsync(appointmentUpdateQuery, commandTimeout: 1000);
                        var appointmentProperties = GetMappedProperties<LeadAppointmentDapperDto>();
                        var appointmentInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "LeadAppointments", appointmentProperties, appointments);
                        await conn.ExecuteAsync(appointmentInsertQuery, commandTimeout: 1000);
                    }
                    //Campaigns
                    var leadCampaigns = leads
                   .Where(lead => lead.Campaigns != null && lead.Campaigns.Any())
                   .SelectMany(lead => lead.Campaigns.Select(i => new Lrb.Application.Lead.Web.Dtos.CampaignLeadDto
                   {
                       LeadsId = lead.Id,
                       CampaignsId = i.Id
                   })).ToList();
                    if (leadCampaigns.Any())
                    {
                        List<string> columnNames = new List<string> { "CampaignsId", "LeadsId" };
                        var campaignInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "CampaignLead", columnNames, leadCampaigns);
                        await conn.ExecuteAsync(campaignInsertQuery, commandTimeout: 1000);
                    }

                    //Custom flags

                    var customFlags = leads
                           .Where(lead => lead.CustomFlags != null && lead.CustomFlags.Any())
                           .SelectMany(lead =>
                           lead.CustomFlags.Select(i =>
                           {
                               var customFlagsDto = i.Adapt<CustomFlagDapperDto>();
                               customFlagsDto.Id = Guid.NewGuid();
                               customFlagsDto.CreatedOn = DateTime.UtcNow;
                               customFlagsDto.CreatedBy = currentUserId;
                               customFlagsDto.UserId = currentUserId;
                               customFlagsDto.LastModifiedBy = currentUserId;
                               customFlagsDto.LeadId = lead.Id;
                               return customFlagsDto;
                           })
                           ).ToList();
                    if (customFlags.Any())
                    {
                        var customFlagsUpdateQuery = QueryGenerator.GenerateDeleteByLeadIdsQuery(DataBaseDetails.LRBSchema, "CustomFlags", leads.Select(i => i.Id).ToList());
                        await conn.ExecuteAsync(customFlagsUpdateQuery, commandTimeout: 1000);
                        var customFlagsProperties = GetMappedProperties<CustomFlagDapperDto>();
                        var customFlagsInsertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "CustomFlags", customFlagsProperties, customFlags);
                        await conn.ExecuteAsync(customFlagsInsertQuery, commandTimeout: 1000);
                    }


                    conn.Close();
                    return (leads.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        public async Task<(List<LeadHistory>?, bool)> UpdateLeadHistoryInDapperAsync(List<LeadHistory> leadHistories, string connectionString, string tenantId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {

                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();

                    // Ensure 'properties' is a list
                    var properties = leadHistories.FirstOrDefault()?.GetType().GetProperties()
                        .Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any())
                        .Select(i => i.Name)
                        .ToList() ?? new List<string>(); // Ensure it's a valid list

                    // Generate the insert query using the properties
                    var insertQuery = QueryGenerator.GenerateUpdateLeadHistoryQuery(tenantId, DataBaseDetails.LRBSchema, "LeadHistories", properties, leadHistories);

                    // Execute the insert query for the list of LeadHistory records
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (leadHistories, true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
    }
}
