﻿using Amazon.DynamoDBv2.Model;
using LeadRotationExecutor.Dtos;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.LeadRotation.Web.Dtos;
using Lrb.Application.LeadRotation.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Shared.Extensions;
using Mapster;
using Microsoft.Graph;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LeadRotationExecutor
{
    public class FunctionEntryPoint : IFunctionEntryPoint
    {
        private readonly ILeadRotationService _leadrotationService;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepository;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<LeadRotationTracker> _leadRotationTrackerRepo;

        public FunctionEntryPoint(ILeadRotationService leadrotationService, IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepository, INotificationSenderService notificationSenderService,
            IUserService userService, IRepositoryWithEvents<LeadRotationTracker> leadRotationTrackerRepo)
        {
            _leadrotationService = leadrotationService;
            _leadRepository = leadRepository;
            _notificationSenderService = notificationSenderService; 
            _userService = userService;
            _leadRotationTrackerRepo = leadRotationTrackerRepo;
        }
        public async Task<(bool, LeadsAssignRotationInfoDto)> ScheduleLeadRotation(Guid leadId, LeadsAssignRotationInfoDto dto)
        {
            try
            {
                Console.WriteLine($"FunctionEntryPoint ScheduleLeadRotation Called: {dto.Serialize()}");
                return await _leadrotationService.RotateAssignLeadsAsync(dto.Adapt<LeadsAssignRotationInfoDto>(), leadId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception Details while calling ScheduleLeadRotation() " + ex.Message);
                throw;
            }

        }

        public async Task<(bool, LeadsAssignRotationInfoDto)> ScheduleRetentionLeadRotation(Guid leadId, LeadsAssignRotationInfoDto dto)
        {
            try
            {
                Console.WriteLine($"FunctionEntryPoint ScheduleRetentionLeadRotation Called: {dto.Serialize()}");
                return await _leadrotationService.RotateTeamRetentionLeadsAsync(dto.Adapt<LeadsAssignRotationInfoDto>(), leadId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception Details while calling ScheduleRetentionLeadRotation() " + ex.Message);
                return (false,dto.Adapt<LeadsAssignRotationInfoDto>());
            }

        }

        public async Task<bool> ScheduleBufferNotification(Guid leadId, LeadsAssignRotationInfoDto dto)
        {
            var fetchLead = (await _leadRepository.FirstOrDefaultAsync(new GetLeadForLeadRotationByIdSpecs(leadId)));
            if (fetchLead != null && dto.AssignedUser != Guid.Empty && !fetchLead.IsPicked && fetchLead.AssignTo == dto?.AssignedUser && dto.ShouldSendPreAlertNotification == true)
            {
                var user = dto?.UserDetails?.FirstOrDefault(i => i.Id == fetchLead.AssignTo) ?? await _userService.GetAsync(fetchLead.AssignTo.ToString(), default);
                if (user != null)
                {
                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.LeadRotationPreAlert, fetchLead, fetchLead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() },bufferTime:dto.BufferTime);
                    dto.ShouldSendPreAlertNotification = false;
                    dto.ShouldSendPostAlertNotification = true;
                    return true;
                }
            }
            else if (fetchLead != null && (fetchLead.IsPicked || dto.AssignedUser == Guid.Empty))
            {
                return false;
            }
            else if( fetchLead != null && dto.AssignedUser != Guid.Empty && fetchLead.AssignTo != dto?.PreviousAssignedUser && dto.ShouldSendPostAlertNotification == true)
            {
                var user = dto?.UserDetails?.FirstOrDefault(i => i.Id == dto?.PreviousAssignedUser) ?? await _userService.GetAsync(dto?.PreviousAssignedUser?.ToString() ?? Guid.Empty.ToString(), default);
                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.LeadRotationPostAlert, fetchLead, dto?.AssignedUser, user.FirstName + " " + user.LastName, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() });
                dto.ShouldSendPostAlertNotification = false;
            }

         return false;
        }
        public async Task<bool> UpdateRotationTracker(string msg, Guid leadId)
        {
            try
            {
                var rotationTracker = (await _leadRotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerSpecs(leadId)));

                switch (rotationTracker.Status)
                {
                    case Lrb.Domain.Enums.UploadStatus.Completed:
                        rotationTracker.Message += msg;
                        return true;

                    case Lrb.Domain.Enums.UploadStatus.Failed:
                    case Lrb.Domain.Enums.UploadStatus.NotEligible:
                        rotationTracker.Message += msg;
                        return true;

                    case Lrb.Domain.Enums.UploadStatus.Initiated:
                    case Lrb.Domain.Enums.UploadStatus.InProgress:
                    case Lrb.Domain.Enums.UploadStatus.Started:
                        rotationTracker.Message += msg;
                        await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                        return false;

                    default:
                        rotationTracker.Message = msg;
                        break;
                }

                await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                return true;
            }
            catch (Exception ex)
            {
                return true;
            }
        }
    }
}
