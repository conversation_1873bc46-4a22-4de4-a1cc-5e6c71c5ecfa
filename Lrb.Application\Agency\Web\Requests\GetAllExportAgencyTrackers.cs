﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Marketing.Web.Dtos;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.Agency.Web.Requests
{
    public class GetAllExportAgencyTrackers : PaginationFilter, IRequest<PagedResponse<ExportMarketingTrackerDto, string>>
    {
    }
    public class GetAllExportCpTrackerHandler : IRequestHandler<GetAllExportAgencyTrackers, PagedResponse<ExportMarketingTrackerDto, string>>
    {
        private readonly IReadRepository<ExportMarketingTracker> _exportCpTrackerRepo;
        public readonly IUserService _userService;
        public GetAllExportCpTrackerHandler(IReadRepository<ExportMarketingTracker> exportCpTrackerRepo,
            IUserService userService)
        {
            _exportCpTrackerRepo = exportCpTrackerRepo;
            _userService = userService;
        }

        public async Task<PagedResponse<ExportMarketingTrackerDto, string>> Handle(GetAllExportAgencyTrackers request, CancellationToken cancellationToken)
        {
            var cpTrackers = await _exportCpTrackerRepo.ListAsync(new GetExportAgencyTrackersSpec(request), cancellationToken);
            var totalCount = await _exportCpTrackerRepo.CountAsync(new GetExportAgencyTrackersCountSpec(), cancellationToken);
            var trackersDto = cpTrackers.Adapt<List<ExportMarketingTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(trackersDto.Select(i => i.CreatedBy.ToString()).ToList(), cancellationToken);
            trackersDto.ForEach(i =>
            {
                i.ExportedUser = (users?.FirstOrDefault(j => j.Id == i.CreatedBy))?.Adapt<ViewUserDto>();
            });
            return new(trackersDto, totalCount);
        }
    }
}
