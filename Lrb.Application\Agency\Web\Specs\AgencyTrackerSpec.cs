﻿using Lrb.Application.Agency.Web.Requests;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.Agency.Web
{
    public class AgencyTrackerSpec : EntitiesByPaginationFilterSpec<BulkMarketingAgencyUploadTracker>
    {
        public AgencyTrackerSpec(GetAllBulkAgencyTrackersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.BulkUploadType == MarketingBulkUploadType.Agency)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportAgencyTrackersSpec : EntitiesByPaginationFilterSpec<ExportMarketingTracker>
    {
        public GetExportAgencyTrackersSpec(GetAllExportAgencyTrackers filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.ExportType == MarketingExportType.ExportAgency).OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportAgencyTrackersCountSpec : Specification<ExportMarketingTracker>
    {
        public GetExportAgencyTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted && i.ExportType == MarketingExportType.ExportAgency);
        }
    }
}
