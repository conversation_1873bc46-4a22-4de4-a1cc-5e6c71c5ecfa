﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace Lrb.Application.Attendance.Mobile.Specs
{
    public class GetAttendanceSettingsSpec : Specification<Domain.Entities.Attendance.AttendenceSettings>
    {
        public GetAttendanceSettingsSpec()
        {
            Query.Where(i => !i.IsDeleted)
                 .Include(i => i.UserShiftTimings)
            .OrderByDescending(i => i.LastModifiedOn);
        }
        public GetAttendanceSettingsSpec(Guid userId)
        {
            Query.Where(i => !i.IsDeleted)
                .Where(i => (i.IsEnabledForAllUsers || EF.Functions.JsonContains(i.UserIds, JsonConvert.SerializeObject(new[] { userId.ToString() }))))
                .Include(i => i.UserShiftTimings)
           .OrderByDescending(i => i.LastModifiedOn);
        }
    }
}
