﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Specs;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Integration.Web;
using Lrb.Application.LeadRotation.Web.Requests;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.PropertyRefrenceInfomation.Web.Specs;
using Lrb.Application.QRFormTemplate.Web.Specs;
using Lrb.Application.Team.Web;
using Lrb.Application.TempProject.Specs;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities.Automation;
using Microsoft.Azure.Cosmos.Serialization.HybridRow;

namespace Lrb.Application.Automation.Requests
{
    public class AddOrUpdateUserAssignmentRequest : UpdateUserAssignmentDto, IRequest<Response<bool>>
    {
    }
    public class AddOrUpdateUserAssignmentRequestHandler : IRequestHandler<AddOrUpdateUserAssignmentRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly IRepositoryWithEvents<Zone> _zoneRepo;
        private readonly IRepositoryWithEvents<City> _cityRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsRepo;
        private readonly IRepositoryWithEvents<FacebookLeadGenForm> _fbFormRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.QRFormTemplate> _qrTemplateRepo;
        private readonly IRepositoryWithEvents<PropertyReferenceInfo> _refrenceInfoRepo;
        private readonly IRepositoryWithEvents<UserAssignmentMetrics> _userAssignmentMetricsRepo;
        private readonly IRepositoryWithEvents<UserAssignmentHistory> _userAssignmentHistoryRepo;
        private readonly IMediator _mediator;
        private readonly IUserAssignmentMetricsService _userAssignmentMetrics;
        public AddOrUpdateUserAssignmentRequestHandler(
            IRepositoryWithEvents<Location> locationRepo,
            IRepositoryWithEvents<Zone> zoneRepo,
            IRepositoryWithEvents<City> cityRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo,
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> fbFormRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            IRepositoryWithEvents<Domain.Entities.QRFormTemplate> qrTemplateRepo,
            IRepositoryWithEvents<PropertyReferenceInfo> refrenceInfoRepo,
            IRepositoryWithEvents<UserAssignmentMetrics> userAssignmentMetricsRepo,
            IRepositoryWithEvents<UserAssignmentHistory> userAssignmentHistoryRepo,
            IMediator mediator,
            IUserAssignmentMetricsService userAssignmentMetrics
            )
        {
            _locationRepo = locationRepo;
            _zoneRepo = zoneRepo;
            _cityRepo = cityRepo;
            _projectRepo = projectRepo;
            _integrationRepo = integrationRepo;
            _fbAdsRepo = fbAdsRepo;
            _fbFormRepo = fbFormRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _qrTemplateRepo = qrTemplateRepo;
            _refrenceInfoRepo = refrenceInfoRepo;
            _userAssignmentHistoryRepo = userAssignmentHistoryRepo;
            _userAssignmentMetricsRepo = userAssignmentMetricsRepo;
            _mediator = mediator;
            _userAssignmentMetrics = userAssignmentMetrics;
        }
        public async Task<Response<bool>> Handle(AddOrUpdateUserAssignmentRequest request, CancellationToken cancellationToken)
        {
            List<Guid>? duplicateUserIds = null;
            List<Guid>? secondaryUserIds = null;
            if (request?.IsDuplicateAssignmentEnabled ?? false)
            {
                duplicateUserIds = request?.DuplicateUserIds;
            }
            if (request?.IsDualAssignmentEnabled ?? false)
            {
                secondaryUserIds = request?.SecondaryUserIds;
            }
            var userAssignment = new UserAssignment();
            Guid? leadRotationId = null;
            if ((request.TeamLeadRotationInfo?.Id != null && request.TeamLeadRotationInfo?.Id != Guid.Empty))
            {
                request.TeamLeadRotationInfo.IntegrationAccountInfoId = request.EntityId;
                leadRotationId = (await _mediator.Send(request.TeamLeadRotationInfo?.Adapt<UpdateTeamRequest>(), cancellationToken))?.Data;
            }
            else if (request.ShouldConfigureLeadRotation == true && request?.TeamLeadRotationInfo != null)
            {
                request.TeamLeadRotationInfo.IntegrationAccountInfoId = request.EntityId;
                leadRotationId = (await _mediator.Send(request.TeamLeadRotationInfo?.Adapt<CreateTeamRequest>(), cancellationToken))?.Data;
            }
            List<UserAssignmentMetrics>? userAssignmentMetrics = null;
            if (request?.CategoryType == AssignmentCategoryType.PercentageBased && (request.UserAssignmentConfigurations?.Any() ?? false))
            {
                userAssignment = new UserAssignment()
                {
                    UserIds = request.UserIds,
                    SecondaryUserIds = secondaryUserIds,
                    EntityId = request.EntityId,
                    DuplicateUserIds = duplicateUserIds,
                    IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default,
                    IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default,
                    ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default,
                    CategoryType = request?.CategoryType ?? default,
                    UserAssignmentType = request?.UserAssignmentType ?? default,
                    UserAssignmentConfigurations = userAssignmentMetrics,
                    ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default,
                    TeamId = leadRotationId ?? request?.TeamLeadRotationInfo?.Id,
                    TeamName = request?.TeamLeadRotationInfo?.Name,
                    ProjectId = request?.Project?.Id,
                    PropertyId = request?.Property?.Id,
                };
            }
            else
            {
                userAssignment = new UserAssignment()
                {
                    UserIds = request.UserIds,
                    SecondaryUserIds = secondaryUserIds,
                    EntityId = request.EntityId,
                    DuplicateUserIds = duplicateUserIds,
                    IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default,
                    IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default,
                    ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default,
                    CategoryType = request?.CategoryType ?? default,
                    UserAssignmentType = request?.UserAssignmentType ?? default,
                    ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default,
                    TeamId = leadRotationId ?? request?.TeamLeadRotationInfo?.Id,
                    TeamName = request?.TeamLeadRotationInfo?.Name,
                    ProjectId = request?.Project?.Id,
                    PropertyId = request?.Property?.Id,
                };
            }
            AssignmentModule? module = null;
            module = await _assignmentModuleRepo.GetByIdAsync(request.ModuleId, cancellationToken);
            if (module != null)
            {
                userAssignment.Module = module;
                switch (module.Name)
                {
                    case AssignmentModule.Project:
                        var project = await _projectRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(request.EntityId), cancellationToken);
                        if (project != null)
                        {
                            if (project.UserAssignment != null)
                            {
                                project.UserAssignment.UserIds = request.UserIds;
                                project.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                project.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                project.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                project.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                project.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                            }
                            else
                            {
                                await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                project.UserAssignment = userAssignment;
                            }
                            await _projectRepo.UpdateAsync(project, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Id.");
                        }
                    case AssignmentModule.Location:
                        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(request.EntityId), cancellationToken);
                        if (location != null)
                        {
                            if (location.UserAssignment != null)
                            {
                                location.UserAssignment.UserIds = request.UserIds;
                            }
                            else
                            {
                                await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                location.UserAssignment = userAssignment;
                            }
                            await _locationRepo.UpdateAsync(location, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Id.");
                        }
                    case AssignmentModule.Zone:
                        var zone = await _zoneRepo.FirstOrDefaultAsync(new ZoneByIdSpec(request.EntityId), cancellationToken);
                        if (zone != null)
                        {
                            if (zone.UserAssignment != null)
                            {
                                zone.UserAssignment.UserIds = request.UserIds;
                            }
                            else
                            {
                                await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                zone.UserAssignment = userAssignment;
                            }
                            await _zoneRepo.UpdateAsync(zone, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Id.");
                        }
                    case AssignmentModule.City:
                        var city = await _cityRepo.FirstOrDefaultAsync(new CityByIdSpec(request.EntityId), cancellationToken);
                        if (city != null)
                        {
                            if (city.UserAssignment != null)
                            {
                                city.UserAssignment.UserIds = request.UserIds;
                            }
                            else
                            {
                                await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                city.UserAssignment = userAssignment;
                            }
                            await _cityRepo.UpdateAsync(city, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Id.");
                        }
                    case AssignmentModule.SubSource:
                        var integrationAccount = await _integrationRepo.FirstOrDefaultAsync(new IntegrationAccountByIdSpec(request.EntityId), cancellationToken);
                        var qrTemplate = await _qrTemplateRepo.FirstOrDefaultAsync(new GetQRFormTemplateByIdSpecs(request.EntityId), cancellationToken);
                        if (integrationAccount != null)
                        {
                            if (request.CategoryType == AssignmentCategoryType.PercentageBased && (request.UserAssignmentConfigurations?.Any() ?? false))
                            {
                                userAssignmentMetrics = await _userAssignmentMetrics.GetUserAssignmentMetrics(request.UserAssignmentConfigurations, integrationAccount.Adapt<AccountInfoDto>());
                            }
                            if (integrationAccount.UserAssignment != null)
                            {
                                integrationAccount.UserAssignment.UserIds = request.UserIds;
                                integrationAccount.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                integrationAccount.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                integrationAccount.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                integrationAccount.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                integrationAccount.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                                integrationAccount.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                integrationAccount.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                integrationAccount.UserAssignment.UserAssignmentConfigurations = userAssignmentMetrics ?? default;
                                integrationAccount.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                integrationAccount.UserAssignment.TotalLeadsCount = 0;
                                integrationAccount.UserAssignment.PropertyId = request?.Property?.Id;
                                integrationAccount.UserAssignment.ProjectId = request?.Project?.Id;
                                integrationAccount.UserAssignment.TeamName = request?.TeamLeadRotationInfo?.Name;
                                integrationAccount.UserAssignment.TeamId = leadRotationId ?? request?.TeamLeadRotationInfo?.Id;
                            }
                            else
                            {
                                if (userAssignmentMetrics?.Any() ?? false)
                                {
                                    userAssignment.UserAssignmentConfigurations = userAssignmentMetrics;
                                }
                                await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                integrationAccount.UserAssignment = userAssignment;
                            }
                            await _integrationRepo.UpdateAsync(integrationAccount, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else if (qrTemplate != null)
                        {
                            if (qrTemplate.UserAssignment != null)
                            {
                                qrTemplate.UserAssignment.UserIds = request.UserIds;
                            }
                            else
                            {
                                await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                qrTemplate.UserAssignment = userAssignment;
                            }
                            await _qrTemplateRepo.UpdateAsync(qrTemplate, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            var fbAd = await _fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(request.EntityId), cancellationToken);
                            if (fbAd != null)
                            {
                                if (request.CategoryType == AssignmentCategoryType.PercentageBased && (request.UserAssignmentConfigurations?.Any() ?? false))
                                {
                                    userAssignmentMetrics = await _userAssignmentMetrics.GetUserAssignmentMetrics(request.UserAssignmentConfigurations, fbAd.Adapt<AccountInfoDto>());
                                }
                                if (fbAd.UserAssignment != null)
                                {
                                    fbAd.UserAssignment.UserIds = request.UserIds;
                                    fbAd.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                    fbAd.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                    fbAd.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                    fbAd.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                    fbAd.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                                    fbAd.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                    fbAd.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                    fbAd.UserAssignment.UserAssignmentConfigurations = userAssignmentMetrics ?? default;
                                    fbAd.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                    fbAd.UserAssignment.TotalLeadsCount = 0;
                                    fbAd.UserAssignment.PropertyId = request?.Property?.Id;
                                    fbAd.UserAssignment.ProjectId = request?.Project?.Id;
                                    fbAd.UserAssignment.TeamName = request?.TeamLeadRotationInfo?.Name;
                                    fbAd.UserAssignment.TeamId = leadRotationId ?? request?.TeamLeadRotationInfo?.Id;
                                }
                                else
                                {
                                    if (userAssignmentMetrics?.Any() ?? false)
                                    {
                                        userAssignment.UserAssignmentConfigurations = userAssignmentMetrics;
                                    }
                                    await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                    fbAd.UserAssignment = userAssignment;
                                }
                                await _fbAdsRepo.UpdateAsync(fbAd, cancellationToken);
                                return new(true, "Assignment details updated successfully.");
                            }
                            else
                            {
                                var fbForm = await _fbFormRepo.FirstOrDefaultAsync(new FacebookFormByIdSpec(request.EntityId), cancellationToken);
                                if (fbForm != null)
                                {
                                    if (request.CategoryType == AssignmentCategoryType.PercentageBased && (request.UserAssignmentConfigurations?.Any() ?? false))
                                    {
                                        userAssignmentMetrics = await _userAssignmentMetrics.GetUserAssignmentMetrics(request.UserAssignmentConfigurations, fbForm.Adapt<AccountInfoDto>());
                                    }
                                    if (fbForm.UserAssignment != null)
                                    {
                                        fbForm.UserAssignment.UserIds = request.UserIds;
                                        fbForm.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                        fbForm.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                        fbForm.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                        fbForm.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                        fbForm.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                                        fbForm.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                        fbForm.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                        fbForm.UserAssignment.UserAssignmentConfigurations = userAssignmentMetrics ?? default;
                                        fbForm.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                        fbForm.UserAssignment.TotalLeadsCount = 0;
                                        fbForm.UserAssignment.PropertyId = request?.Property?.Id;
                                        fbForm.UserAssignment.ProjectId = request?.Project?.Id;
                                        fbForm.UserAssignment.TeamName = request?.TeamLeadRotationInfo?.Name;
                                        fbForm.UserAssignment.TeamId = leadRotationId ?? request?.TeamLeadRotationInfo?.Id;
                                    }
                                    else
                                    {
                                        if (userAssignmentMetrics?.Any() ?? false)
                                        {
                                            userAssignment.UserAssignmentConfigurations = userAssignmentMetrics;
                                        }
                                        await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                        fbForm.UserAssignment = userAssignment;
                                    }
                                    await _fbFormRepo.UpdateAsync(fbForm, cancellationToken);
                                    return new(true, "Assignment details updated successfully.");
                                }
                                else
                                {
                                    return new(false, "Please provide valid Id.");
                                }
                            }

                        }
                    case AssignmentModule.ReferenceId:
                        var refrenceInfo = await _refrenceInfoRepo.FirstOrDefaultAsync(new GetRefrenceInfoUserAssignmentByIdSpecs(request.EntityId), cancellationToken);
                        if (refrenceInfo != null)
                        {
                            if (refrenceInfo.UserAssignment != null)
                            {
                                refrenceInfo.UserAssignment.UserIds = request.UserIds;
                                refrenceInfo.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                refrenceInfo.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                refrenceInfo.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                refrenceInfo.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                refrenceInfo.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                            }
                            else
                            {
                                await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                refrenceInfo.UserAssignment = userAssignment;
                            }
                            await _refrenceInfoRepo.UpdateAsync(refrenceInfo, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Id.");
                        }
                    default:
                        return new(false);
                }
            }
            else
            {
                return new(false, "Please provide a valid module Id.");
            }
        }
    }
}
