﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Campaigns.Spec;
using Lrb.Domain.Entities.Marketing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Campaigns.Request
{
    public class GetAllBulkCamapignTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
    }
    public class GetAllBulkCamapignTrackersRequestHandler : IRequestHandler<GetAllBulkCamapignTrackersRequest, PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> _trackerRepo;

        public GetAllBulkCamapignTrackersRequestHandler(IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> trackerRepo)
        {
            _trackerRepo = trackerRepo;
        }
        public async Task<PagedResponse<BulkMarketingAgencyUploadTracker, string>> Handle(GetAllBulkCamapignTrackersRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _trackerRepo.ListAsync(new CamapignTrackerSpec(request), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
