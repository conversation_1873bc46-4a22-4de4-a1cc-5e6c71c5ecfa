﻿using Lrb.Application.Agency.Web.Requests;
using Lrb.Application.Campaigns.Request;
using Lrb.Application.Campaigns.web.Request;
using Lrb.Domain.Entities.Marketing;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Campaigns.Spec
{

    public class GetAllCampaignsSpac : Specification<Domain.Entities.Campaign>
    {
        public GetAllCampaignsSpac(Guid id)
        {
            Query.Where(i => !i.IsDeleted && id == i.Id);
        }
    }
    public class GetCampignByNameSpec : Specification<Domain.Entities.Campaign>
    {
        public GetCampignByNameSpec(string campaignName)
        {
            Query.Where(i => !i.IsDeleted &&
                             !string.IsNullOrWhiteSpace(campaignName) &&
                             i.Name.ToLower().Trim().Replace(" ", "").Equals(campaignName.ToLower().Trim().Replace(" ", "")));
        }
    }
    public class GetCampaignsBySpec : Specification<Campaign>
    {
        public GetCampaignsBySpec(List<string> campaignNames)
        {
            Query.Where(i => !i.IsDeleted && !string.IsNullOrWhiteSpace(i.Name) && i.Name.Equals(campaignNames))
                .Include(i => i.Flags);

        }


    }
    public class GetCampaignsByFiltersSpec : Specification<Domain.Entities.Campaign>
    {
        public GetCampaignsByFiltersSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && id == i.Id)
                .Include(i => i.Flags);
        }
    }

    public class DeleteCampaignSpec : Specification<Domain.Entities.Campaign>
    {
        public DeleteCampaignSpec(List<Guid> campaignIds)
        {

            Query.Where(i => campaignIds.Contains(i.Id) && !i.IsDeleted);

        }
    }

    public class DeleteCampaignIvrAssignmentsSpec : Specification<Domain.Entities.IVRAssignment>
    {
        public DeleteCampaignIvrAssignmentsSpec(List<Guid> campaignIds)
        {
            Query.Where(i => !i.IsDeleted && campaignIds.Contains(i.Campaign.Id))
                .Include(i => i.Campaign);
        }
    }
    public class DeleteLeadsForCampaignSpec : Specification<Domain.Entities.Lead>
    {
        public DeleteLeadsForCampaignSpec(List<Guid> campaignIds)
        {
            //Query.Where(i => agencyIds.Contains(i.Id) && !i.IsDeleted).Include(i => i.Address).Include(i => i.Leads).Include(i => i.Prospects);
            Query.Where(i => !i.IsDeleted && i.Campaigns.Any(a => campaignIds.Contains(a.Id))).Include(i => i.Campaigns);
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Agencies)
                .Include(i => i.Campaigns)
                .Include(i => i.CustomLeadStatus)
                .Where(i => !i.CustomLeadStatus.IsDeleted)
                 .Include(i => i.TagInfo)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location);
        }
    }




    public class DeleteProspectsForCampaignSpec : Specification<Domain.Entities.Prospect>
    {
        public DeleteProspectsForCampaignSpec(List<Guid> campaignIds)
        {
            Query.Where(i => !i.IsDeleted && i.Campaigns.Any(a => campaignIds.Contains(a.Id))).Include(i => i.Campaigns);
            Query.Where(i => !i.IsDeleted)
              .Include(i => i.Agencies)
              .Include(I => I.Campaigns)
             .Include(i => i.Address)
              .ThenInclude(i => i.Location)
              .ThenInclude(i => i.Zone)
              .ThenInclude(i => i.City)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.Addresses)
              .ThenInclude(i => i.Location)
              .ThenInclude(i => i.Zone)
              .ThenInclude(i => i.City)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.PropertyType)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.Source)
              .Include(i => i.ChannelPartners)
              .Include(i => i.Projects)
              .Include(i => i.Properties)
              .Include(i => i.Status)
              .Include(i => i.Agencies);
        }
    }


    public class DeleteCampaignIntegrationAccountSpec : Specification<Domain.Entities.IntegrationAccountInfo>
    {

        public DeleteCampaignIntegrationAccountSpec(List<Guid> campaignId)
        {
            Query.Where(i => !i.IsDeleted && campaignId.Contains(i.Campaign.Id))
                .Include(i => i.Campaign);
        }
    }

    public class DeleteCampaignQrFormTemplateAccountSpec : Specification<Domain.Entities.QRFormTemplate>
    {
        public DeleteCampaignQrFormTemplateAccountSpec(List<Guid> campaignIds)
        {
            Query.Where(i => !i.IsDeleted && campaignIds.Contains(i.Campaign.Id))
                .Include(i => i.Campaign);
        }

    }

    public class DeleteCampaignFaceBookLeadGenFormSpec : Specification<Domain.Entities.FacebookLeadGenForm>
    {
        public DeleteCampaignFaceBookLeadGenFormSpec(List<Guid> campaignIds)
        {
            Query.Where(i => !i.IsDeleted && campaignIds.Contains(i.CustomCampaign.Id))
               .Include(i => i.CustomCampaign);
        }
    }

    public class DeleteCamapignFacebookAdsInfo : Specification<Domain.Entities.FacebookAdsInfo>
    {
        public DeleteCamapignFacebookAdsInfo(List<Guid> campaignIds)
        {
            Query.Where(i => !i.IsDeleted && campaignIds.Contains(i.CustomCampaign.Id))
       .Include(i => i.CustomCampaign);
        }
    }

    public class CamapignTrackerSpec : EntitiesByPaginationFilterSpec<BulkMarketingAgencyUploadTracker>
    {
        public CamapignTrackerSpec(GetAllBulkCamapignTrackersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.BulkUploadType == MarketingBulkUploadType.Campaign)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportCamapignTrackersSpec : EntitiesByPaginationFilterSpec<ExportMarketingTracker>
    {
        public GetExportCamapignTrackersSpec(GetAllExportCampaignTrackers filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.ExportType == MarketingExportType.ExportCamapign).OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportCamapignTrackersCountSpec : Specification<ExportMarketingTracker>
    {
        public GetExportCamapignTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted && i.ExportType == MarketingExportType.ExportCamapign);
        }
    }
    public class GetAllBulkCampaignsSpecs : Specification<Lrb.Domain.Entities.Campaign>
    {
        public GetAllBulkCampaignsSpecs()
        {
            Query.Where(i => !i.IsDeleted);
        }

    }

    public class GetBulkMarketingCampaignByTrackerIdSpec : Specification<BulkMarketingAgencyUploadTracker>
    {
        public GetBulkMarketingCampaignByTrackerIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }
    public class CampaignAffiliatedCountFiltersSpec : Specification<Domain.Entities.Campaign>
    {
        public CampaignAffiliatedCountFiltersSpec(GetAllCampaingsRequest request, List<Guid> ids)
        {
            var campaigns = request.CampaignNames?.Select(name => name.Replace(" ", "").ToLower()).ToArray();
            var searchText = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText;
            var dateType = (int?)request.DateType;
            var fromDate = request.FromDate;
            var toDate = request.ToDate;
            Query.Where(a => !a.IsDeleted && ids.Contains(a.Id));
            if (!string.IsNullOrEmpty(searchText))
            {
                Query.Where(a =>
                    a.Name.ToLower().Contains(searchText.ToLower())
                    );
            }
            if (fromDate.HasValue && toDate.HasValue)
            {
                if (dateType == 0)
                {
                    Query.Where(a => (a.CreatedOn >= fromDate && a.CreatedOn <= toDate) ||
                                     (a.LastModifiedOn >= fromDate && a.LastModifiedOn <= toDate));
                }
                else if (dateType == 1)
                {
                    Query.Where(a => a.CreatedOn >= fromDate && a.CreatedOn <= toDate);
                }
                else if (dateType == 2)
                {
                    Query.Where(a => a.LastModifiedOn >= fromDate && a.LastModifiedOn <= toDate);
                }
            }
            var a = Query.OrderByDescending(a => a.LastModifiedOn)
              .Skip(request.PageSize * (request.PageNumber - 1))
              .Take(request.PageSize);

        }
    }
    public class ExportCampaignsByFiltersSpec : Specification<Domain.Entities.Campaign>
    {
        public ExportCampaignsByFiltersSpec(RunAWSBatchForMarketingCampaignRequest request, List<Guid> ids)
        {
            var agencies = request.CampaignNames?.Select(name => name.Replace(" ", "").ToLower()).ToArray();
            var searchText = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText;
            var dateType = (int?)request.DateType;
            var fromDate = request.FromDate;
            var toDate = request.ToDate;
            Query.Where(a => !a.IsDeleted && ids.Contains(a.Id));
            /*   if (agencies.Any())
               {
                   Query.Where(a => agencies.Contains(a.Name.ToLower().Replace(" ", "")));
               }*/
            if (!string.IsNullOrEmpty(searchText))
            {
                Query.Where(a =>
                    a.Name.ToLower().Contains(searchText.ToLower()));
            }
            if (fromDate.HasValue && toDate.HasValue)
            {
                if (dateType == 0)
                {
                    Query.Where(a => (a.CreatedOn >= fromDate && a.CreatedOn <= toDate) ||
                                     (a.LastModifiedOn >= fromDate && a.LastModifiedOn <= toDate));
                }
                else if (dateType == 1)
                {
                    Query.Where(a => a.CreatedOn >= fromDate && a.CreatedOn <= toDate);
                }
                else if (dateType == 2)
                {
                    Query.Where(a => a.LastModifiedOn >= fromDate && a.LastModifiedOn <= toDate);
                }
            }
            var a = Query.OrderByDescending(a => a.LastModifiedOn)
              .Skip(request.PageSize * (request.PageNumber - 1))
              .Take(request.PageSize);

        }
    }
    public class GetCampaignByNameSpecs : Specification<Lrb.Domain.Entities.Campaign>
    {
        public GetCampaignByNameSpecs(string name)
        {
            Query.Where(i => !i.IsDeleted && i.Name.ToLower().Trim() == name.ToLower().Trim());
        }
    }
    public class GetFlagsSpec : Specification<Domain.Entities.Flag>
    {
        public GetFlagsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }

    }
    public class GetFlagsByIdsSpec : Specification<Domain.Entities.Flag>
    {
        public GetFlagsByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }
}


