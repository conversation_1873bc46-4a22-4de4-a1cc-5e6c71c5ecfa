﻿using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.ChannelPartner.Web.Request
{

    public class GetAllBulkCpTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
    }
    public class GetAllBulkCpTrackersRequestHandler : IRequestHandler<GetAllBulkCpTrackersRequest, PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> _trackerRepo;

        public GetAllBulkCpTrackersRequestHandler(IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> trackerRepo)
        {
            _trackerRepo = trackerRepo;
        }
        public async Task<PagedResponse<BulkMarketingAgencyUploadTracker, string>> Handle(GetAllBulkCpTrackersRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _trackerRepo.ListAsync(new ChannelPartnersTrackerSpec(request), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
