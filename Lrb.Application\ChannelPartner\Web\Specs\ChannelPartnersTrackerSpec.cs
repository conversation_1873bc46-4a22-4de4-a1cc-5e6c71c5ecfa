﻿using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.ChannelPartner.Web
{
    public class ChannelPartnersTrackerSpec : EntitiesByPaginationFilterSpec<BulkMarketingAgencyUploadTracker>
    {
        public ChannelPartnersTrackerSpec(GetAllBulkCpTrackersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.BulkUploadType == MarketingBulkUploadType.ChannelPartner)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
}
