﻿using Lrb.Application.Common.IVR.Common.Dtos;

namespace Lrb.Application.Common.IVR
{
    public interface IIVRService : ITransientService
    {
        public Task<Response<ClickToCallCommonResponseDto>> ClickToCall(ClickToCallCommonDto dto, IntegrationAccountInfoCommonDto integrationAccountInfoDto);
        public Task<Response<ClickToCallCommonResponseDto>> BuildRequest(ClickToCallCommonDto dto, IntegrationAccountInfoCommonDto integrationAccountInfoDto);
        public Task<Response<ClickToCallCommonResponseDto>> ClickToCallConfig(ClickToCallCommonDto dto, IntegrationAccountInfoCommonDto integrationAccountInfoDto, IVROutboundConfiguration ivrOutboundConfig, TempVariable? tempVariable);
    }
}
