﻿namespace Lrb.Application.CustomEmail.Web.Dtos
{
    public class CreateEmailTemplateDto : BaseEmailTemplateDto
    {

    }
    public class UpdateEmailTemplateDto : BaseEmailTemplateDto
    {

    }
    public class ViewEmailTemplateDto : BaseEmailTemplateDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
    }
    public class BaseEmailTemplateDto : IDto
    {
        public Event? Event { get; set; }
        public string? Subject { get; set; }
        public string? Body { get; set; }
        public string? Name { get; set; }
        public TemplateType? TemplateType { get; set; }
        public EmailBodyType? BodyType { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; }
        public bool IsPrimary { get; set; }
        public List<string>? AttachmentUrls { get; set; }
        public bool IsLeadSpecific { get; set; }
    }
}
