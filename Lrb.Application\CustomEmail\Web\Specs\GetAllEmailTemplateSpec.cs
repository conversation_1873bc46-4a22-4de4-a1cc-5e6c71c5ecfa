﻿using Lrb.Application.CustomEmail.Web.Requests;

namespace Lrb.Application.CustomEmail.Web.Specs
{
    public class GetAllEmailTemplateSpec : Specification<EmailTemplates>
    {
        public GetAllEmailTemplateSpec(GetEmailTemplateRequest request) 
        {
            Query.Where(i => !i.IsDeleted && i.CreatedOn >= (request.FromDate ?? DateTime.MinValue) && i.CreatedOn <= (request.ToDate ?? DateTime.MaxValue));
            
            if (request.PageNumber != 0 && request.PageSize != 0)
            {
                Query.Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .OrderBy(i => i.CreatedOn);
            }
        }
        public class GetCountEmailTemplateSpec : Specification<EmailTemplates>
        {
            public GetCountEmailTemplateSpec(DateTime? fromDate, DateTime? toDate)
            {
                Query.Where(i => !i.IsDeleted && i.CreatedOn >= (fromDate ?? DateTime.MinValue) && i.CreatedOn <= (toDate ?? DateTime.MaxValue));
            }
        }
    }
}
