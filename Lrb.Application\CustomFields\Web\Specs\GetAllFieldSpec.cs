﻿namespace Lrb.Application.CustomFields.Web
{
    public class GetAllFieldSpec : EntitiesByPaginationFilterSpec<Field>
    {
        public GetAllFieldSpec(GetAllFieldsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetAllFieldCountSpec : Specification<Field>
    {
        public GetAllFieldCountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
    public class GetFieldByNameSpec : Specification<Field>
    {
        public GetFieldByNameSpec(string name, Guid? id = default)
        {
            if (id != null && id != default)
            {
                Query.Where(i => !i.IsDeleted && i.Name.ToLower() == name.ToLower() && i.Id != id);
            }
            else
            {
                Query.Where(i => !i.IsDeleted && i.Name.ToLower() == name.ToLower());
            }
        }
    }
}
