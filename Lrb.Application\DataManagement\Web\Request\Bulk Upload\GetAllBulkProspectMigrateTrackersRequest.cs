﻿using Lrb.Domain.Entities.DataManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Request.Bulk_Upload
{
    public class GetAllBulkProspectMigrateTrackersRequest : PaginationFilter, IRequest<PagedResponse<DataMigrateTracker, string>>
    {
    }
    public class GetAllBulkMigrateTrackersRequestHandler : IRequestHandler<GetAllBulkProspectMigrateTrackersRequest, PagedResponse<DataMigrateTracker, string>>
    {
        private readonly IRepositoryWithEvents<DataMigrateTracker> _trackerRepo;

        public GetAllBulkMigrateTrackersRequestHandler(IRepositoryWithEvents<DataMigrateTracker> trackerRepo)
        {
            _trackerRepo = trackerRepo;
        }
        public async Task<PagedResponse<DataMigrateTracker, string>> Handle(GetAllBulkProspectMigrateTrackersRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _trackerRepo.ListAsync(new DataMigrateTrackerSpec(request), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(cancellationToken);
            return new(trackers, totalCount);
        }
    }

    public class DataMigrateTrackerSpec : EntitiesByPaginationFilterSpec<DataMigrateTracker>
    {
        public DataMigrateTrackerSpec(GetAllBulkProspectMigrateTrackersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
}
