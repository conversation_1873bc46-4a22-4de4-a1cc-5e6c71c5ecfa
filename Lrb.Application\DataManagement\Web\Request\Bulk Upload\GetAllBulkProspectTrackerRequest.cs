﻿using Lrb.Application.Lead.Web.Requests.BulkUploadTracker;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Request.Bulk_Upload
{
    public class GetAllBulkProspectTrackerRequest : PaginationFilter, IRequest<PagedResponse<BulkProspectUploadTracker, string>>
    {
    }

    public class GetAllBulkProspectTrackerRequestHandler : IRequestHandler<GetAllBulkProspectTrackerRequest, PagedResponse<BulkProspectUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkProspectUploadTracker> _bulkProspectTracker;
        public GetAllBulkProspectTrackerRequestHandler(IRepositoryWithEvents<BulkProspectUploadTracker> bulkProspectTracker)
        {
            _bulkProspectTracker = bulkProspectTracker;
        }

        public async Task<PagedResponse<BulkProspectUploadTracker, string>> Handle(GetAllBulkProspectTrackerRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var trackers = await _bulkProspectTracker.ListAsync(new BulkProspectUploadSpec(request), cancellationToken);
                var totalCount = await _bulkProspectTracker.CountAsync(cancellationToken);
                return new(trackers, totalCount);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }

    public class BulkProspectUploadSpec : EntitiesByPaginationFilterSpec<BulkProspectUploadTracker>
    {
        public BulkProspectUploadSpec(GetAllBulkProspectTrackerRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
}
