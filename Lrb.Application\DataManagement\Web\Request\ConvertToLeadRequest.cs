﻿using Lrb.Application.Common.PushNotification;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Request.CommonHandler;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class ConvertToLeadRequest : IRequest<Response<AssignmentProspectDto>>
    {
        public Guid Id { get; set; }
        public Guid? AssignTo { get; set; }
        public Guid? LeadStatusId { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public List<string>? Projects { get; set; }
        public string? ConvertedDateTime { get; set; }
    }

    public class ConvertToLeadRequestHander : DataCommonRequestHandler, IRequestHandler<ConvertToLeadRequest, Response<AssignmentProspectDto>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> _leadHistoryRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.TempProjects> _tempProjectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ProspectHistory> _prospcetHistoryRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        public ConvertToLeadRequestHander(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            ICurrentUser currentUser,
            //IRepositoryWithEvents<MasterLeadStatus> masterLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> leadHistoryRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.TempProjects> tempProjectRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.ProspectHistory> prospcetHistoryRepo,
            IServiceProvider serviceProvider,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo
            ) : base(serviceProvider)
        {
            _prospectRepo = prospectRepo;
            _leadRepo = leadRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _currentUser = currentUser;
            //_masterLeadStatusRepo = masterLeadStatusRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _notificationSenderService = notificationSenderService;
            _userService = userService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
            _tempProjectRepo = tempProjectRepo;
            _prospcetHistoryRepo = prospcetHistoryRepo;
            _globalSettingsRepo = globalSettingsRepo;
        }

        public async Task<Response<AssignmentProspectDto>> Handle(ConvertToLeadRequest request, CancellationToken cancellationToken)
        {
            var response = new Response<AssignmentProspectDto>();
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var existingProspect = await _prospectRepo.FirstOrDefaultAsync(new GetProspectByIdSpecs(request.Id));
            if (existingProspect == null)
            {
                throw new NotFoundException("Data not Found");
            }
            if (existingProspect.IsConvertedToLead)
            {
                return new()
                {
                    Message = "Selected Data already converted to lead.",
                     //Data = false,
                    Succeeded = false
                };
            }
            var oldProspect = existingProspect?.Adapt<ViewProspectDto>();
            var lead = MapProspectToLead(existingProspect, cancellationToken);
            var currentUserId = _currentUser.GetUserId(); ;
            string createdBy = null;
            try
            {
                var userDetails = await _userService.GetAsync(currentUserId.ToString(), cancellationToken);
                createdBy = $"{userDetails.FirstName} {userDetails.LastName}";
            }
            catch
            {

            }
            if (!string.IsNullOrEmpty(request.Notes))
            {
                lead.Notes = $"{request.Notes}\nQualifiedBy: {createdBy}" + (request.ConvertedDateTime != null ? $"\nConvertedDatetime: {request.ConvertedDateTime}" : string.Empty);
            }
            else
            {
                lead.Notes = $"{lead.Notes ?? string.Empty}\nQualifiedBy: {createdBy}{(request.ConvertedDateTime != null ? $"\nConvertedDatetime: {request.ConvertedDateTime}" : "")}";
            }
            var newStatus = (await _customLeadStatusRepo.ListAsync(cancellationToken));

            if (request.LeadStatusId != null && request.LeadStatusId != Guid.Empty)
            {
                var customLeadStatus = newStatus.Where(i => i.Id == request.LeadStatusId).FirstOrDefault();
                if (customLeadStatus?.Level < 1)
                {
                    throw new ArgumentException("Provide Child Id of this Status");
                }
                lead.CustomLeadStatus = customLeadStatus ?? newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
                lead.ScheduledDate = request.ScheduledDate;
            }
            else
            {
                lead.CustomLeadStatus = newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
            }
            try
            {
                #region Projects
                List<Lrb.Domain.Entities.Project> tempProjects = new();
                request.Projects = (request.Projects?.Any() ?? false) ? request.Projects.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (request.Projects?.Any() ?? false)
                {
                    foreach (var newProject in request.Projects)
                    {
                        Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                        if (existingProject != null)
                        {
                            tempProjects.Add(existingProject);
                        }
                        else
                        {
                            Domain.Entities.Project tempProject = new() { Name = newProject };
                            tempProject = await _projectRepo.AddAsync(tempProject, cancellationToken);
                            tempProjects.Add(tempProject);
                        }
                    }
                    lead.Projects = tempProjects;
                }
                #endregion
            }
            catch (Exception ex)
            {
                throw;
            }

            lead.TagInfo = new LeadTag();

            #region Enquiry
            lead.TagInfo = new LeadTag();
            var enquiry = existingProspect?.Enquiries?.Where(i => i.IsPrimary).FirstOrDefault();
           
            List<MasterPropertyType>? propertyTypeses = null;
            if (enquiry?.PropertyTypes != null)
            {
                propertyTypeses = await _propertyTypeRepo.ListAsync(new Property.Web.Specs.GetMasterPropertyTypeSpec(enquiry.PropertyTypes.Select(i => i.Id).ToList()));
            }
            if (enquiry != null)
            {
                var leadEnquiry = CreateLeadEnquiryFromProspectEnquiry(enquiry, existingProspect?.PossesionDate);
                leadEnquiry.PropertyType = propertyTypeses?.FirstOrDefault();
                leadEnquiry.PropertyTypes = propertyTypeses;
                leadEnquiry.IsPrimary = true;
                lead.IsConvertedFromData = true;
                lead.QualifiedBy = currentUserId;
                lead.Enquiries = new List<LeadEnquiry>();
                lead.Enquiries.Add(leadEnquiry);
            }

            #endregion
            if (request.AssignTo == Guid.Empty || request.AssignTo == default)
            {
                lead.AssignTo = currentUserId;
                //lead.AssignedFrom = currentUserId;
            }
            else
            {
                lead.AssignTo = request?.AssignTo ?? Guid.Empty;
                //lead.AssignedFrom = currentUserId;
            }
            // Set OriginalOwner to the assigned user when first assigned
            if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
            {
                lead.OriginalOwner = lead.AssignTo;
            }
            try
            {
                Guid? parentLeadId = null;
                var rootLead = await _leadRepo.FirstOrDefaultAsync(new GetContcactNoSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                if (rootLead != null )
                { if (globalSettings.IsStickyAgentEnabled == true)
                    {
                        lead.RootId = rootLead.Id;
                        lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                        lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                        rootLead.ChildLeadsCount += 1;
                        try
                        {
                            await _leadRepo.UpdateAsync(rootLead);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "ConvertToLeadRequestHander -> Handle() -> UpdateParentLead()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                    else if (rootLead?.AssignTo!=null)
                    {
                        response.Data = new AssignmentProspectDto
                        {
                            ProspectId = request.Id,
                            AssignTo = rootLead.AssignTo
                        };

                        if (response.Data == null)
                        {
                            response.Data = new AssignmentProspectDto();
                        }
                    }
                        if ((response.Data.AssignTo!=null && response.Data.AssignTo != lead.AssignTo) || response.Data == null )
                        {
                            lead.RootId = rootLead.Id;
                            lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                            lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                            rootLead.ChildLeadsCount += 1;
                            try
                            {
                                await _leadRepo.UpdateAsync(rootLead);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "ConvertToLeadRequestHander -> Handle() -> UpdateParentLead()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            try
            {/*
                if (response.Data == null)
                {
                    response.Data = new AssignmentProspectDto();
                }*/
                if ( response.Data == null || (response.Data.AssignTo != null && response.Data.AssignTo != lead.AssignTo))
                {
                    lead = await _leadRepo.AddAsync(lead);

                    var prospectstatus = (await _prospectStatusRepo.ListAsync(new GetQualifiedStatusSpecs(), cancellationToken)).FirstOrDefault();
                    existingProspect.IsQualified = true;
                    existingProspect.IsConvertedToLead = true;
                    existingProspect.ConvertedBy = currentUserId;
                    existingProspect.ConvertedDate = DateTime.UtcNow;
                    existingProspect.Status = prospectstatus;
                    if (existingProspect.QualifiedDate == null)
                    {
                        existingProspect.QualifiedDate = DateTime.UtcNow;
                    }
                    if (existingProspect.QualifiedBy == null)
                    {
                        existingProspect.QualifiedBy = currentUserId;
                    }
                    await _prospectRepo.UpdateAsync(existingProspect);


                    #region History
                    var statuses = await _prospectStatusRepo.ListAsync();
                    var propertyTypes = await _propertyTypeRepo.ListAsync();
                    var sources = await _prospectSourceRepo.ListAsync();
                    var prospectVM = existingProspect.Adapt<ViewProspectDto>();
                    var userIds = new List<string?>
                    {
                         prospectVM.LastModifiedBy.ToString(),
                         prospectVM.SourcingManager.ToString(),
                         prospectVM.ClosingManager.ToString(),
                    };

                    var userDetails = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
                    prospectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(prospectVM, userDetails, cancellationToken);
                    var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                    await _prospcetHistoryRepo.AddRangeAsync(histories);
                }
                #endregion
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ConvertToLeadRequestHander -> Handle() -> Add Lead()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            if (response.Data == null || (response.Data.AssignTo != null && response.Data.AssignTo != lead.AssignTo))
            {
                var leadDto = lead.Adapt<ViewLeadDto>();
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                try
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "ConvertToLeadRequestHander -> Handle() -> Add History()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }

                if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != _currentUser.GetUserId())
                {
                    try
                    {
                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, status: leadDto.Status?.DisplayName);
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
                if (response.Data != null && response?.Data?.AssignTo == lead.AssignTo)
                {
                    return new Response<AssignmentProspectDto>
                    {
                        /*  Data = new AssignmentProspectDto
                          {
                              // Initialize properties of AssignmentProspectDto
                              ProspectId = request.Id,
                              AssignTo = request.AssignTo
                          },*/
                        Data = response.Data,
                        Succeeded = true,
                        Message = "Prospect could not be Converted to lead"
                    };
                }
            
            else
            {
                return new Response<AssignmentProspectDto>
                {

                    Data = response.Data = null,
                    Succeeded = true,
                    Message = "successfully Converted to lead"
                };

            }
        }
    }
}
