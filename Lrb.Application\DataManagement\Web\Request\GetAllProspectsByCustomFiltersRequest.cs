﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Lead.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class GetAllProspectsByCustomFiltersRequest : GetAllProspectParameter, IRequest<PagedResponse<ViewProspectDto, string>>
    {

    }

    public class GetAllProspectsByCustomFiltersRequestHandler : IRequestHandler<GetAllProspectsByCustomFiltersRequest, PagedResponse<ViewProspectDto, string>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly IProspectRepository _efProspectRepository;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _customProspectRepo;
        private readonly IRepositoryWithEvents<CustomFilter> _customfilterRepo;
        public GetAllProspectsByCustomFiltersRequestHandler(ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            IProspectRepository efProspectRepository,
            IRepositoryWithEvents<CustomProspectStatus> customProspectRepo,
            IRepositoryWithEvents<CustomFilter> customfilterRepo)
        {
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _efProspectRepository = efProspectRepository;
            _customProspectRepo = customProspectRepo;
            _customfilterRepo = customfilterRepo;
        }

        public async Task<PagedResponse<ViewProspectDto, string>> Handle(GetAllProspectsByCustomFiltersRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var userId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var isAdmin = await _dapperRepository.IsAdminV2Async(userId, tenantId ?? string.Empty);
                List<Guid> subIds = new();
                CustomFilter? filter = null;
                List<Prospect>? prospects = null;
                int totalCount = default;
                try
                {
                    if (request?.AssignTo?.Any() ?? false)
                    {
                        if (request?.IsWithTeam ?? false)
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                        }
                        else
                        {
                            subIds = request?.AssignTo ?? new List<Guid>();
                        }
                    }
                    else
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllProspects, isAdmin))?.ToList() ?? new();
                    }
                }
                catch
                {

                }
                if (request.CustomFilterId == null || request.CustomFilterId == default)
                {
                    filter = await _customfilterRepo.FirstOrDefaultAsync(new GetProspectDefaultCustomFilterSpec(userId, isAdmin), cancellationToken);
                }
                else
                {
                    filter = await _customfilterRepo.FirstOrDefaultAsync(new GetProspectCustomFiltersSpec(request.CustomFilterId ?? Guid.Empty), cancellationToken);
                }
                List<CustomProspectStatus> statuses = null;
                try
                {
                    if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch != null && request.PropertyToSearch.Contains("Status"))
                    {
                        statuses = await _customProspectRepo.ListAsync(new GetProspectStatusByNameSpecs(request.ProspectSearch));
                    }
                }
                catch(Exception ex)
                {
                }
                var tasks1 = new Task[]
                {
                Task.Run(async () => prospects = (await _efProspectRepository.GetAllProspectsByCustomFiltersForWebAsync(filter ?? new CustomFilter(), request.Adapt<GetAllProspectParameter>(), subIds, userId,isAdmin, statuses)).ToList()),
                Task.Run(async () => totalCount = await _efProspectRepository.GetProspectsCountByCustomFiltersForWebAsync(filter ?? new CustomFilter(), request.Adapt<GetAllProspectParameter>(), subIds, userId,isAdmin, prospectStatuses:statuses)),
                };
                await Task.WhenAll(tasks1);
                List<ViewProspectDto> prospectsView = prospects?.Adapt<List<ViewProspectDto>>() ?? new();
                return new(prospectsView, totalCount);
            }
            catch (Exception ex) { return new() { Message = ex.Message }; }
        }
    }
}
