﻿using Lrb.Application.DataManagement.Web.Export.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Specs
{
    public class GetExportProspectTrackerRequestSpecs : EntitiesByPaginationFilterSpec<ExportProspectTracker>
    {
        public GetExportProspectTrackerRequestSpecs(GetExportTrackerRequest filter) : base(filter) 
        {
            Query.Where(i => !i.IsDeleted).OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportProspectTrackerCountSpecs : Specification<ExportProspectTracker>
    {
        public GetExportProspectTrackerCountSpecs()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}
