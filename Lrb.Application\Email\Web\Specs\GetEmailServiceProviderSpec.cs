﻿using Lrb.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Email.Web.Specs
{
    public class GetEmailServiceProviderSpec : Specification<EmailServiceProvider>
    {
        public GetEmailServiceProviderSpec(EmailServiceProviders currentServiceProvider)
        {
            Query.Where(i => !i.IsDeleted && i.ServiceProvider == currentServiceProvider)
                .Include(i => i.EmailApiIntegrationData)
                .Include(i => i.EmailConfigurationData);
        }
    }
}
