﻿using Lrb.Application.Common.IVR.Common.Dtos;
using Lrb.Application.Integration.Mobile;

namespace Lrb.Application.GlobalSettings.Mobile
{
    public class GetGlobalSettingsRequest : IRequest<Response<ViewGlobalSettingsDto>>
    {
    }
    public class GetGlobalSettingsRequestHandler : IRequestHandler<GetGlobalSettingsRequest, Response<ViewGlobalSettingsDto>>
    {
        private readonly IReadRepository<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateFeatureInfo;

        public GetGlobalSettingsRequestHandler(IReadRepository<Domain.Entities.GlobalSettings> globalSettingsRepository,
                                                IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync,
                                                IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateFeatureInfo)
        {
            _globalSettingsRepository = globalSettingsRepository;
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _duplicateFeatureInfo = duplicateFeatureInfo;
        }

        public async Task<Response<ViewGlobalSettingsDto>> Handle(GetGlobalSettingsRequest request, CancellationToken cancellationToken)
        {
            var globalSettings = (await _globalSettingsRepository.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
            var duplicateFeatureInfo = (await _duplicateFeatureInfo.ListAsync(cancellationToken)).FirstOrDefault();

            ViewGlobalSettingsDto viewGlobalSettings = new();
            if (globalSettings != null)
            {
                viewGlobalSettings = globalSettings.Adapt<ViewGlobalSettingsDto>();
            }
            if (duplicateFeatureInfo != null)
            {
                viewGlobalSettings.DuplicateLeadFeatureInfo = duplicateFeatureInfo.Adapt<DuplicateLeadFeatureInfo>();
            }
            var accs = (await _integrationAccountInfoRepositoryAsync.ListAsync(new IntegrationAccountInfoByVirtualNumberSpec(LeadSource.IVR), cancellationToken));
            var integrationAccountsInfo = accs.FirstOrDefault(i => i.IVRCallType == IVRType.Outbound && i.IsPrimary);
            var integrationAccountInfoDto = integrationAccountsInfo?.Adapt<IntegrationAccountInfoCommonDto>();
            if ((integrationAccountInfoDto?.IsPrimary ?? false)
                && (integrationAccountsInfo?.IVRCallType == IVRType.Outbound))
            {
                viewGlobalSettings.IsIVROutboundEnabled = true;
                viewGlobalSettings.IsVirtualNumberRequiredForOutbound = ((integrationAccountsInfo?.IVROutboundConfiguration?.IsVirtualNumberRequired ?? false)
                                                                        || (integrationAccountsInfo?.IVRApiConfiguration?.IsVirtualNumberRequiredForOutbound ?? false));
            }
            return new(viewGlobalSettings);
        }
    }
}