﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.GlobalSettings.Mobile.Requests
{
    public class UpdateLeadNotesSettingRequest : LeadNotesSetting, IRequest<Response<bool>>
    {
    }

    public class UpdateLeadNotesSettingRequestHandler : IRequestHandler<UpdateLeadNotesSettingRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        public UpdateLeadNotesSettingRequestHandler(IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepository)
        {
            _globalSettingsRepository = globalSettingsRepository;
        }
        public async Task<Response<bool>> Handle(UpdateLeadNotesSettingRequest request, CancellationToken cancellationToken)
        {
            var existingGlobalSettings = (await _globalSettingsRepository.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
            string notesSetting = null;
            if (request != null)
            {
                notesSetting = JsonConvert.SerializeObject(request);
            }
            if (existingGlobalSettings == null)
            {
                Domain.Entities.GlobalSettings globalSettings = new();
                globalSettings.LeadNotesSetting = notesSetting ?? null;
                await _globalSettingsRepository.AddAsync(globalSettings, cancellationToken);
                return new(true);
            }
            else
            {
                existingGlobalSettings.LeadNotesSetting = notesSetting;
                await _globalSettingsRepository.UpdateAsync(existingGlobalSettings, cancellationToken);
                return new(true);
            }
        }
    }
}
