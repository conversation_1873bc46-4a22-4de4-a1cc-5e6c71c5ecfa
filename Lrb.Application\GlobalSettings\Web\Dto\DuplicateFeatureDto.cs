﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.GlobalSettings.Web.Dto
{
    public class DuplicateFeatureDto
    {
        public bool AllowAllDuplicates { get; set; }
        public bool IsFeatureAdded { get; set; }
        public bool IsSourceBased { get; set; }
        public bool IsSubSourceBased { get; set; }
        public bool IsProjectBased { get; set; }
        public bool IsLocationBased { get; set; }
        public List<Guid?>? StutusIds { get; set; }
    }
}
