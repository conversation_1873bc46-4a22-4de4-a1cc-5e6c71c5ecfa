﻿using Lrb.Application.Common.IVR.Common.Dtos;
using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Application.Integration.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Domain.Constants;

namespace Lrb.Application.GlobalSettings.Web
{
    public class GetGlobalSettingsRequest : IRequest<Response<ViewGlobalSettingsDto>>
    {
    }
    public class GetGlobalSettingsRequestHandler : IRequestHandler<GetGlobalSettingsRequest, Response<ViewGlobalSettingsDto>>
    {
        private readonly IReadRepository<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        private readonly IReadRepository<DuplicateLeadFeatureInfo> _duplicateLeadFeatureInfo;
        private readonly INpgsqlRepository _npgsqlRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        public GetGlobalSettingsRequestHandler(IReadRepository<Domain.Entities.GlobalSettings> globalSettingsRepository,
            IReadRepository<DuplicateLeadFeatureInfo> duplicateLeadFeatureInfo, INpgsqlRepository npgsqlRepository,
            ICurrentUser currentUser,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync
            )
        {
            _globalSettingsRepository = globalSettingsRepository;
            _duplicateLeadFeatureInfo = duplicateLeadFeatureInfo;
            _npgsqlRepository = npgsqlRepository;
            _currentUser = currentUser;
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
        }

        public async Task<Response<ViewGlobalSettingsDto>> Handle(GetGlobalSettingsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var globalSettings = (await _globalSettingsRepository.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
                ViewGlobalSettingsDto viewGlobalSettings = new();
                if (globalSettings != null)
                {
                    viewGlobalSettings = globalSettings.Adapt<ViewGlobalSettingsDto>();
                }
                var duplicateFeatureInfo = await _duplicateLeadFeatureInfo.FirstOrDefaultAsync(new GetFeatureInfoSpec(), cancellationToken);
                viewGlobalSettings.DuplicateFeatureInfo = duplicateFeatureInfo?.Adapt<DuplicateFeatureDto>();
                var accs = (await _integrationAccountInfoRepositoryAsync.ListAsync(new IntegrationAccountInfoByVirtualNumberSpec(LeadSource.IVR), cancellationToken));
                var integrationAccountsInfo = accs.FirstOrDefault(i => i.IVRCallType == IVRType.Outbound && i.IsPrimary);
                var integrationAccountInfoDto = integrationAccountsInfo?.Adapt<IntegrationAccountInfoCommonDto>();
                if ((integrationAccountInfoDto?.IsPrimary ?? false)
                    && (integrationAccountsInfo?.IVRCallType == IVRType.Outbound))
                {
                    viewGlobalSettings.IsIVROutboundEnabled = true;
                    viewGlobalSettings.IsVirtualNumberRequiredForOutbound = ((integrationAccountsInfo?.IVROutboundConfiguration?.IsVirtualNumberRequired ?? false)
                                                                            || (integrationAccountsInfo?.IVRApiConfiguration?.IsVirtualNumberRequiredForOutbound ?? false));
                }
                viewGlobalSettings.MethodTypes = IVRConstants.GetMethods();
                viewGlobalSettings.ContentTypes = IVRConstants.GetContentTypes();
                return new(viewGlobalSettings);
            }
            catch (Exception ex) 
            {
                return new() { Message = ex.Message };
            }
        }
    }
}