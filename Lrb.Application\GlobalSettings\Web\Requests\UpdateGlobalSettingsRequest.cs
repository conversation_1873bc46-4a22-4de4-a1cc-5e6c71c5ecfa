﻿using Lrb.Domain.Entities;
using Newtonsoft.Json;

namespace Lrb.Application.GlobalSettings.Web
{
    public class UpdateGlobalSettingsRequest : UpdateGlobalSettingsDto, IRequest<Response<bool>>
    {
    }
    public class UpdateGlobalSettingsRequestHandler : IRequestHandler<UpdateGlobalSettingsRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateFeatureInfo;
        private readonly IDuplicateFeatureRepository _duplicateRepo;

        public UpdateGlobalSettingsRequestHandler(IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepository,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateFeatureInfo,
            IDuplicateFeatureRepository duplicateRepo)
        {
            _globalSettingsRepository = globalSettingsRepository;
            _duplicateFeatureInfo = duplicateFeatureInfo;
            _duplicateRepo = duplicateRepo;
        }

        public async Task<Response<bool>> Handle(UpdateGlobalSettingsRequest request, CancellationToken cancellationToken)
        {
            var existingGlobalSettings = (await _globalSettingsRepository.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
            //  var duplicateFeatureInfo = (await _duplicateFeatureInfo.ListAsync(cancellationToken)).FirstOrDefault();

            string? callSettings = null;
            string? notificationSettingsString = null;
            string? notesSetting = null;
            string? propertySetting = null;
            string? projectSetting = null;
            string? otpSettings = null;
            string? generalSettings = null;

            if (request.CallSettings != null)
            {
                callSettings = JsonConvert.SerializeObject(request.CallSettings);
            }
            if (request.GeneralSettings != null)
            {
                generalSettings = JsonConvert.SerializeObject(request.GeneralSettings);
            }
            if (request?.NotificationSettings != null)
            {
                //request.NotificationSettings.ModuleSettings = request.NotificationSettings.ModuleSettings.DistinctBy(i => i.Event).ToList();

                NotificationSettings notificationSettings = request.NotificationSettings.Adapt<NotificationSettings>();
                if (existingGlobalSettings?.NotificationSettings != null)
                {
                    NotificationSettings? existingNotificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(existingGlobalSettings.NotificationSettings);
                    notificationSettings.IsAdminEnabled = request.NotificationSettings.IsAdminEnabled ?? existingNotificationSettings?.IsAdminEnabled ?? false;
                    notificationSettings.IsManagerEnabled = request.NotificationSettings.IsManagerEnabled ?? existingNotificationSettings?.IsManagerEnabled ?? false;
                    notificationSettings.WhatsAppNotificationSettings = request.NotificationSettings.WhatsAppNotificationSettings ?? (notificationSettings.WhatsAppNotificationSettings ?? new());
                    notificationSettings.WhatsAppNotificationSettings.LeadAfterEvents ??= new();
                    notificationSettings.WhatsAppNotificationSettings.MinutesAfter ??= new();
                    notificationSettings.IsGeneralManagerEnabled = request.NotificationSettings.IsGeneralManagerEnabled ?? existingNotificationSettings?.IsGeneralManagerEnabled ?? false;
                    notificationSettingsString = JsonConvert.SerializeObject(notificationSettings);

                }

            }
            if (request?.LeadNotesSetting != null)
            {
                notesSetting = JsonConvert.SerializeObject(request.LeadNotesSetting);
            }
            if (request?.LeadPropertySetting != null)
            {
                propertySetting = JsonConvert.SerializeObject(request.LeadPropertySetting);
            }
            if (request?.LeadProjectSetting != null)
            {
                projectSetting = JsonConvert.SerializeObject(request.LeadProjectSetting);
            }
            if (request?.OTPSettings != null)
            {
                otpSettings = JsonConvert.SerializeObject(request.OTPSettings);
            }
            if (existingGlobalSettings == null)
            {
                Domain.Entities.GlobalSettings globalSettings = new();
                globalSettings.CallSettings = callSettings ?? CreateCallSettings();
                globalSettings.GeneralSettings = generalSettings ?? CreateGeneralSettings();
                globalSettings.NotificationSettings = notificationSettingsString ?? CreateNotificationSettings();
                globalSettings.HasInternationalSupport = request?.HasInternationalSupport ?? default;
                globalSettings.IsLeadsExportEnabled = request?.IsLeadsExportEnabled ?? default;
                globalSettings.IsLeadSourceEditable = request?.IsLeadSourceEditable ?? default;
                globalSettings.DayStartTime = request?.DayStartTime ?? default;
                globalSettings.DayEndTime = request?.DayEndTime ?? default;
                globalSettings.IsGoogleMapLocationEnabled = request?.IsGoogleMapLocationEnabled ?? default;
                globalSettings.IsZoneLocationEnabled = request?.IsZoneLocationEnabled ?? default;
                globalSettings.IsCallDetectionActivated = request?.IsCallDetectionActivated ?? default;
                globalSettings.LeadNotesSetting = notesSetting ?? CreateLeadNotesSetting();
                globalSettings.IsWhatsAppEnabled = request?.IsWhatsAppEnabled ?? default;
                globalSettings.IsGoogleMapLocationEnabled = request?.IsGoogleMapLocationEnabled ?? default;
                globalSettings.IsZoneLocationEnabled = request?.IsZoneLocationEnabled ?? default;
                globalSettings.IsMaskedLeadContactNo = request?.IsMaskedLeadContactNo ?? default;
                globalSettings.IsPropertiesExportEnabled = request?.IsPropertiesExportEnabled ?? default;
                globalSettings.IsPropertyAssignmentEnabled = request?.IsPropertyAssignmentEnabled ?? default;
                globalSettings.IsMicrositeFeatureEnabled = request?.IsMicrositeFeatureEnabled ?? default;
                globalSettings.IsDualOwnershipEnabled = request?.IsDualOwnershipEnabled ?? default;
                globalSettings.IsExportDataEnabled = request?.IsExportDataEnabled ?? default;
                globalSettings.IsStickyAgentEnabled = request?.IsStickyAgentEnabled ?? default;
                globalSettings.LeadPropertySetting = propertySetting ?? CreateLeadPropertySetting();
                globalSettings.LeadProjectSetting = projectSetting ?? CreateLeadProjectSetting();
                globalSettings.OTPSettings = otpSettings;
                globalSettings.IsTimeZoneEnabled = request?.IsTimeZoneEnabled ?? default;
                globalSettings.ShouldHideDashBoard = request?.ShouldHideDashBoard ?? default;
                if (request?.Countries?.Any() ?? false)
                {
                    var countries = request.Countries.Adapt<List<CountryInfo>>();
                    globalSettings.Countries = countries;
                }
                globalSettings.IsCopyPasteEnabled = request?.IsCopyPasteEnabled ?? default;
                globalSettings.IsScreenshotEnabled = request?.IsScreenshotEnabled ?? default;
                globalSettings.IsLeadRotationEnabled = request?.IsLeadRotationEnabled ?? default;
                globalSettings.IsStickyAgentOverriddenEnabled = request?.IsStickyAgentOverriddenEnabled ?? default;
                globalSettings.DirectionOfLeadCreation = request?.DirectionOfLeadCreation ?? default;
                globalSettings.DirectionOfLeadModification = request?.DirectionOfLeadModification ?? default;

                globalSettings.IsProjectMicrositeEnabled = request?.IsProjectMicrositeEnabled ?? default;
                globalSettings.IsCustomStatusEnabled = request?.IsCustomStatusEnabled ?? default;
                globalSettings.IsWhatsAppDeepIntegration = request?.IsWhatsAppDeepIntegration ?? default;
                globalSettings.IsAssignedCallLogsEnabled = request?.IsAssignedCallLogsEnabled ?? default;
                globalSettings.IsTeamLeadRotationEnabled = request?.IsTeamLeadRotationEnabled ?? default;
                globalSettings.ShouldEnablePropertyListing = request?.ShouldEnablePropertyListing ?? default;
                globalSettings.CanAccessAnonymousApis = request?.CanAccessAnonymousApis ?? default;
                globalSettings.IsCustomLeadFormEnabled = request?.IsCustomLeadFormEnabled ?? default;
                globalSettings.IsMobileCallEnabled = request?.IsMobileCallEnabled ?? default;
                globalSettings.ShouldEnableEnquiryForm = request?.ShouldEnableEnquiryForm ?? default;
                globalSettings.ShouldRenameSiteVisitColumn = request?.ShouldRenameSiteVisitColumn ?? default;
                globalSettings.IsPastDateSelectionEnabled = request?.IsPastDateSelectionEnabled ?? default;
                if (request?.DefaultValues?.Any() == true)
                {
                    globalSettings.DefaultValues = request.DefaultValues;
                }
                globalSettings.IsProjectsExportEnabled = request?.IsProjectsExportEnabled ?? default;

                globalSettings.IsReportsConfigurationEnabled = request?.IsReportsConfigurationEnabled ?? default;
                globalSettings.ShowMoreMicrositeProperties = request?.ShowMoreMicrositeProperties ?? default;

                await _globalSettingsRepository.AddAsync(globalSettings, cancellationToken);

                return new(true);
            }
            else
            {
                existingGlobalSettings.NotificationSettings = notificationSettingsString ?? existingGlobalSettings.NotificationSettings;
                existingGlobalSettings.CallSettings = callSettings ?? existingGlobalSettings.CallSettings;
                existingGlobalSettings.GeneralSettings = generalSettings ?? existingGlobalSettings.GeneralSettings;
                existingGlobalSettings.HasInternationalSupport = request?.HasInternationalSupport ?? existingGlobalSettings.HasInternationalSupport;
                existingGlobalSettings.IsLeadsExportEnabled = request?.IsLeadsExportEnabled ?? existingGlobalSettings.IsLeadsExportEnabled;
                existingGlobalSettings.IsLeadSourceEditable = request?.IsLeadSourceEditable ?? existingGlobalSettings.IsLeadSourceEditable;
                existingGlobalSettings.DayStartTime = request?.DayStartTime ?? existingGlobalSettings.DayStartTime;
                existingGlobalSettings.DayEndTime = request?.DayEndTime ?? existingGlobalSettings.DayEndTime;
                existingGlobalSettings.IsCallDetectionActivated = request?.IsCallDetectionActivated ?? existingGlobalSettings.IsCallDetectionActivated;
                existingGlobalSettings.IsGoogleMapLocationEnabled = request?.IsGoogleMapLocationEnabled ?? existingGlobalSettings.IsGoogleMapLocationEnabled;
                existingGlobalSettings.IsZoneLocationEnabled = request?.IsZoneLocationEnabled ?? existingGlobalSettings.IsGoogleMapLocationEnabled;
                existingGlobalSettings.LeadNotesSetting = notesSetting ?? existingGlobalSettings.LeadNotesSetting;
                existingGlobalSettings.IsWhatsAppEnabled = request?.IsWhatsAppEnabled ?? existingGlobalSettings.IsWhatsAppEnabled;
                existingGlobalSettings.IsMaskedLeadContactNo = request?.IsMaskedLeadContactNo ?? existingGlobalSettings.IsMaskedLeadContactNo;
                existingGlobalSettings.IsPropertiesExportEnabled = request?.IsPropertiesExportEnabled ?? existingGlobalSettings.IsPropertiesExportEnabled;
                existingGlobalSettings.IsDualOwnershipEnabled = request?.IsDualOwnershipEnabled ?? existingGlobalSettings.IsPropertiesExportEnabled;
                existingGlobalSettings.IsPropertyAssignmentEnabled = request?.IsPropertyAssignmentEnabled ?? existingGlobalSettings.IsPropertyAssignmentEnabled;
                existingGlobalSettings.IsMicrositeFeatureEnabled = request?.IsMicrositeFeatureEnabled ?? existingGlobalSettings.IsMicrositeFeatureEnabled;
                existingGlobalSettings.IsExportDataEnabled = request?.IsExportDataEnabled ?? existingGlobalSettings.IsExportDataEnabled;
                existingGlobalSettings.IsStickyAgentEnabled = request?.IsStickyAgentEnabled ?? existingGlobalSettings.IsStickyAgentEnabled;
                existingGlobalSettings.LeadProjectSetting = projectSetting ?? existingGlobalSettings.LeadProjectSetting;
                existingGlobalSettings.LeadPropertySetting = propertySetting ?? existingGlobalSettings.LeadPropertySetting;
                existingGlobalSettings.OTPSettings = otpSettings ?? existingGlobalSettings.OTPSettings;
                existingGlobalSettings.IsTimeZoneEnabled =request?.IsTimeZoneEnabled ?? existingGlobalSettings.IsTimeZoneEnabled;
                if (request?.Countries?.Any() ?? false)
                {
                    var countries = request.Countries.Adapt<List<CountryInfo>>();
                    existingGlobalSettings.Countries = countries;
                }
                existingGlobalSettings.IsCopyPasteEnabled = request?.IsCopyPasteEnabled ?? existingGlobalSettings.IsCopyPasteEnabled;
                existingGlobalSettings.IsScreenshotEnabled = request?.IsScreenshotEnabled ?? existingGlobalSettings.IsScreenshotEnabled;

                existingGlobalSettings.IsProjectMicrositeEnabled = request?.IsProjectMicrositeEnabled ?? existingGlobalSettings.IsProjectMicrositeEnabled;
                existingGlobalSettings.ShouldHideDashBoard = request?.ShouldHideDashBoard ?? existingGlobalSettings.ShouldHideDashBoard;
                existingGlobalSettings.IsLeadRotationEnabled = request?.IsLeadRotationEnabled ?? existingGlobalSettings.IsLeadRotationEnabled;
                existingGlobalSettings.IsStickyAgentOverriddenEnabled = request?.IsStickyAgentOverriddenEnabled ?? existingGlobalSettings.IsStickyAgentOverriddenEnabled;
                existingGlobalSettings.IsCustomStatusEnabled = request?.IsCustomStatusEnabled ?? existingGlobalSettings.IsCustomStatusEnabled;
                existingGlobalSettings.IsWhatsAppDeepIntegration = request?.IsWhatsAppDeepIntegration ?? existingGlobalSettings.IsWhatsAppDeepIntegration;
                existingGlobalSettings.DirectionOfLeadCreation = request?.DirectionOfLeadCreation ?? existingGlobalSettings.DirectionOfLeadCreation;
                existingGlobalSettings.DirectionOfLeadModification = request?.DirectionOfLeadModification ?? existingGlobalSettings.DirectionOfLeadModification;
                existingGlobalSettings.IsAssignedCallLogsEnabled = request?.IsAssignedCallLogsEnabled ?? existingGlobalSettings.IsAssignedCallLogsEnabled;
                existingGlobalSettings.IsTeamLeadRotationEnabled = request?.IsTeamLeadRotationEnabled ?? existingGlobalSettings.IsTeamLeadRotationEnabled;
                existingGlobalSettings.ShouldEnablePropertyListing = request?.ShouldEnablePropertyListing ?? existingGlobalSettings.ShouldEnablePropertyListing;
                existingGlobalSettings.CanAccessAnonymousApis = request?.CanAccessAnonymousApis ?? existingGlobalSettings.CanAccessAnonymousApis;
                existingGlobalSettings.IsCustomLeadFormEnabled = request?.IsCustomLeadFormEnabled ?? existingGlobalSettings.IsCustomLeadFormEnabled;
                existingGlobalSettings.IsMobileCallEnabled = request?.IsMobileCallEnabled ?? existingGlobalSettings.IsMobileCallEnabled;
                existingGlobalSettings.IsProjectsExportEnabled = request?.IsProjectsExportEnabled ?? existingGlobalSettings.IsProjectsExportEnabled;
                existingGlobalSettings.ShouldEnableEnquiryForm = request?.ShouldEnableEnquiryForm ?? existingGlobalSettings.ShouldEnableEnquiryForm;
                existingGlobalSettings.ShouldRenameSiteVisitColumn = request?.ShouldRenameSiteVisitColumn ?? existingGlobalSettings.ShouldRenameSiteVisitColumn;
                existingGlobalSettings.IsPastDateSelectionEnabled = request?.IsPastDateSelectionEnabled ?? existingGlobalSettings.IsPastDateSelectionEnabled;
                if(request?.DefaultValues?.Any() == true)
                {
                    existingGlobalSettings.DefaultValues = request.DefaultValues;
                }
                existingGlobalSettings.IsReportsConfigurationEnabled = request?.IsReportsConfigurationEnabled ?? existingGlobalSettings.IsReportsConfigurationEnabled;
                existingGlobalSettings.ShowMoreMicrositeProperties = request?.ShowMoreMicrositeProperties ?? existingGlobalSettings.ShowMoreMicrositeProperties;
                await _globalSettingsRepository.UpdateAsync(existingGlobalSettings, cancellationToken);
            }
            return new(true);
        }
        private string CreateNotificationSettings()
        {
            NotificationSettings notificationSettings = new();
            notificationSettings.ModuleSettings.Add(CreateBaseNotificationSettings(ModuleName.Todo, Event.ScheduledTaskReminder, new List<int>() { 15, 30 }));
            notificationSettings.ModuleSettings.Add(CreateBaseNotificationSettings(ModuleName.Lead, Event.CallbackReminder, new List<int>() { 15, 30 }));
            notificationSettings.ModuleSettings.Add(CreateBaseNotificationSettings(ModuleName.Lead, Event.ScheduleSiteVisitReminder, new List<int>() { 15, 30 }));
            notificationSettings.ModuleSettings.Add(CreateBaseNotificationSettings(ModuleName.Lead, Event.ScheduleMeetingReminder, new List<int>() { 15, 30 }));
            notificationSettings.ChannelSettings = new NotificationChannelSettings()
            {
                IsEmailNotificationEnabled = false,
                IsPushNotificationEnabled = true,
            };
            notificationSettings.EmailNotificationSettings = new EmailNotificationSettings()
            {
                IsContactNoMaskingEnabled = true,
            };
            notificationSettings.WhatsAppNotificationSettings = new();
            notificationSettings.IsAdminEnabled = false;
            notificationSettings.IsManagerEnabled = false;
            notificationSettings.IsGeneralManagerEnabled = false;
            var result = JsonConvert.SerializeObject(notificationSettings);
            return result;
        }
        private BaseNotificationSettings CreateBaseNotificationSettings(ModuleName moduleName, Event @event, List<int> minutesBefore)
        {
            BaseNotificationSettings baseNotificationSettings = new();
            baseNotificationSettings.ModuleName = moduleName;
            baseNotificationSettings.Event = @event;
            baseNotificationSettings.MinutesBefore = minutesBefore;
            return baseNotificationSettings;
        }
        private string CreateCallSettings()
        {
            CallSettings callSetting = new();
            callSetting.CallType = CallType.NotSet;
            var result = JsonConvert.SerializeObject(callSetting);
            return result;
        }
        private string CreateLeadNotesSetting()
        {
            LeadNotesSetting notesSetting = new();
            var result = JsonConvert.SerializeObject(notesSetting);
            return result;
        }

        private string CreateLeadProjectSetting()
        {
            LeadProjectSetting projectSetting = new();
            var result = JsonConvert.SerializeObject(projectSetting);
            return result;
        }

        private string CreateLeadPropertySetting()
        {
            LeadPropertySetting propertySetting = new();
            var result = JsonConvert.SerializeObject(propertySetting);
            return result;
        }
        private string CreateGeneralSettings()
        {
            GeneralSettings generalSetting = new();
            var result = JsonConvert.SerializeObject(generalSetting);
            return result;
        }
    }
}
