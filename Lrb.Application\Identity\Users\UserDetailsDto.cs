namespace Lrb.Application.Identity.Users;

public class UserDetailsDto
{
    public Guid Id { get; set; }

    public string? UserName { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public string? Email { get; set; }

    public bool IsActive { get; set; } = true;

    public bool EmailConfirmed { get; set; }

    public string? PhoneNumber { get; set; }

    public string? ImageUrl { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? LastModifiedOn { get; set; }
    public DateTime? CreatedOn { get; set; }
    public Guid CreatedBy { get; set; }
    public Guid LastModifiedBy { get; set; }    
    public bool IsMFAEnabled { get; set; }
    public string? OTP { get; set; }
    public DateTime? OTPUpdatedOn { get; set; }
    public string? TimeZoneInfo { get; set; }
    public string? LicenseNo { get; set; }
    public bool? IsLocked { get; set; }
    public Guid? GeneralManagerId { get; set; }
}