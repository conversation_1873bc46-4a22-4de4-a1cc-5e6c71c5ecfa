﻿using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.LeadGenRequests;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests.Bayut
{
    public class BayutNewWAPushIntegrationRequest : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public BayutNewWAPushIntegrationRequest(HttpRequest request, string tenant, string base64)
        {
            HttpRequest = request;
            TenantId = tenant;
            ApiKey = base64;
        }
        public class BayutNewWAPushIntegrationRequestHandler : IRequestHandler<BayutNewWAPushIntegrationRequest, Response<bool>>
        {
            private readonly IMediator _mediator;
            private readonly Serilog.ILogger _logger;
            private readonly IRepositoryWithEvents<IntegrationAccountInfo> _intAccRepo;
            public BayutNewWAPushIntegrationRequestHandler(IMediator mediator, Serilog.ILogger logger, IRepositoryWithEvents<IntegrationAccountInfo> intAccRepo)
            {
                _mediator = mediator;
                _logger = logger;
                _intAccRepo = intAccRepo;
            }

            public async Task<Response<bool>> Handle(BayutNewWAPushIntegrationRequest request, CancellationToken cancellationToken)
            {
                var accountId = request.ApiKey.GetAccountId();
                var integrationAccount = await _intAccRepo.GetByIdAsync(accountId, cancellationToken);
                if (integrationAccount == null)
                {
                    _logger.Error("BayutNewWAPushIntegrationRequestHandler -> POST(Bayut) -> Integration Account not found for ApiKey: " + request.ApiKey);
                    return new(false, "Integration Account not found");
                }
                var httpRequest = request.HttpRequest;
                var bodyInString = "";
                BayutNewWADto? payload = null;
                if (request.HttpRequest.HasFormContentType)
                {
                    var form = await httpRequest.ReadFormAsync();
                    var formData = form.ToDictionary(x => x.Key, x => x.Value.ToString());
                    bodyInString = JsonConvert.SerializeObject(formData);
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutNewWADto>(bodyInString);
                }
                else if (httpRequest.QueryString.HasValue)
                {
                    var queryParamsData = httpRequest.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                    bodyInString = JsonConvert.SerializeObject(queryParamsData);
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutNewWADto>(bodyInString);
                }
                else
                {
                    Stream stream = httpRequest.Body;
                    HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
                    bodyInString = await reader.ReadToEndAsync();
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutNewWADto>(bodyInString);
                }
                if (payload != null)
                {
                    _logger.Information("BayutNewWAPushIntegrationRequestHandler -> POST(Bayut) -> called, Dto: " + payload);
                    CreateLeadGenRequest leadGenRequest = new(LeadSource.Bayut, payload);

                    await _mediator.Send(leadGenRequest);
                    var lead = new WhatsAppListingSitesIntegrationRequest()
                    {
                        LeadSource = Domain.Enums.LeadSource.Bayut,
                        ApiKey = request.ApiKey,
                        Name = payload?.enquirer?.name ?? "Unknown",
                        Mobile = payload?.enquirer?.phone_number ?? string.Empty,
                        Notes = $"{payload?.message ?? string.Empty} \nTracking Url: { payload?.enquirer?.contact_link ?? string.Empty} \n Property Url: {payload?.listing?.url ?? string.Empty} \n Reference: {payload?.listing?.reference ?? string.Empty}",
                        Link = payload?.enquirer?.contact_link ?? string.Empty,
                        ReferenceId = payload?.listing?.reference ?? string.Empty,
                        PrimaryUser = payload?.agent?.name ?? string.Empty,
                    };
                    await _mediator.Send(lead);
                }
                return new(true);
            }
        }
    }
}
