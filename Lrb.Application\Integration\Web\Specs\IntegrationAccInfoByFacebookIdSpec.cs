﻿namespace Lrb.Application.Integration.Web.Specs
{
    public class IntegrationAccInfoByFacebookIdOrIdSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccInfoByFacebookIdOrIdSpec(Guid fbId)
        {
            Query
                .Include(i => i.UserAssignment)
                     .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Agency)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.ChannelPartner)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Property)
                 .Include(i => i.Assignment)
                    .ThenInclude(i => i.Campaign)
                .Where(i => (i.FacebookAccountId == fbId || i.Id == fbId) && !i.IsDeleted);
        }
        public IntegrationAccInfoByFacebookIdOrIdSpec(List<Guid> fbIds)
        {
            Query
                .Include(i => i.UserAssignment)
                     .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Agency)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.ChannelPartner)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Property)
                 .Include(i => i.Assignment)
                    .ThenInclude(i => i.Campaign)
                .Where(i => (fbIds.Contains(i.Id) || fbIds.Contains(i.FacebookAccountId)) && !i.IsDeleted);
        }
    }
    public class AdsByIdsSpec : Specification<FacebookAdsInfo>
    {
        public AdsByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));

        }

    }
    public class FormsByIdsSpec : Specification<FacebookLeadGenForm>
    {
        public FormsByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }

    }
}
