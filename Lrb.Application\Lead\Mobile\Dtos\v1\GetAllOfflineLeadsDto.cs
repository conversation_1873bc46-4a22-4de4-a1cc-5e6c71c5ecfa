﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Mobile.Dtos.v1
{
    public class GetAllOfflineLeadsDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? ContactNo{ get; set; }
        public string? AlternateContactNo {  get; set; }
        public Guid AssignTo { get; set; }
        public bool? IsDeleted { get; set; }
        public DateTime? LastModifiedOn {  get; set; }
        public bool? IsArchived { get; set; }
    }
}
