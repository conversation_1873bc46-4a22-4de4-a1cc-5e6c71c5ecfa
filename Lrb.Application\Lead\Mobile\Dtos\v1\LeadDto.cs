﻿
using Lrb.Application.Agency.Web;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Dtos.v2;
using Lrb.Application.LeadCallLog.Mobile;
using Lrb.Application.Property.Mobile;

namespace Lrb.Application.Lead.Mobile
{
    public class UpdateLeadDto : CreateLeadDto
    {
        public Guid LeadStatusId { get; set; }
    }
    public class CreateLeadDto : BaseLeadDto
    {
        public CreateLeadEnquiryDto? Enquiry { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public List<string>? ChannelPartnerList { get; set; }
    }
    public class ViewLeadDto : BaseLeadDto
    {
        private List<LeadAppointmentDto>? appointments;
        private List<LeadAppointmentDto>? meetingsDone;
        private List<LeadAppointmentDto>? meetingsNotDone;
        private List<LeadAppointmentDto>? siteVisitsDone;
        private List<LeadAppointmentDto>? siteVisitsNotDone;
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public UserDetails.Mobile.UserDto? LastModifiedByUser { get; set; }
        public UserDetails.Mobile.UserDto? AssignedUser { get; set; }
        public UserDetails.Mobile.UserDto? AssignedFromUser { get; set; }
        public LeadStatusDto? Status { get; set; }
        public ViewLeadEnquiryDto? Enquiry { get; set; }
        public Dictionary<int, Dictionary<int, Dictionary<DateTime, string>>>? CallRecordingUrls { get; set; }
        public LeadFilterTypeMobile LeadFilterKey { get; set; }
        public Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>> LeadHistory { get; set; }
        public List<LeadDocument>? Documents { get; set; }
        public List<ProjectDto>? Projects { get; set; }
        public List<PropertyDto>? Properties { get; set; }
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
        //public List<MeetingOrVisitDto>? MeetingsDone { get; set; }
        //public List<MeetingOrVisitDto>? MeetingsNotDone { get; set; }
        //public List<MeetingOrVisitDto>? SiteVisitsDone { get; set; }
        //public List<MeetingOrVisitDto>? SiteVisitsNotDone { get; set; }
        public IList<BookedDetailsDto>? BookedDetails { get; set; }
        public List<LeadAppointmentDto>? Appointments
        {
            get => appointments;
            set
            {
                appointments = value;
                MeetingsDone = value?.Where(x => x.Type == AppointmentType.Meeting && x.IsDone)?.ToList();
                MeetingsNotDone = value?.Where(x => x.Type == AppointmentType.Meeting && !x.IsDone)?.ToList();
                SiteVisitsDone = value?.Where(x => x.Type == AppointmentType.SiteVisit && x.IsDone)?.ToList();
                SiteVisitsNotDone = value?.Where(x => x.Type == AppointmentType.SiteVisit && !x.IsDone)?.ToList();
            }
        }
        public List<LeadAppointmentDto>? MeetingsDone { get => Appointments?.Where(x => x.Type == AppointmentType.Meeting && x.IsDone)?.ToList(); set => meetingsDone = value; }
        public List<LeadAppointmentDto>? MeetingsNotDone { get => Appointments?.Where(x => x.Type == AppointmentType.Meeting && !x.IsDone)?.ToList(); set => meetingsNotDone = value; }
        public List<LeadAppointmentDto>? SiteVisitsDone { get => Appointments?.Where(x => x.Type == AppointmentType.SiteVisit && x.IsDone)?.ToList(); set => siteVisitsDone = value; }
        public List<LeadAppointmentDto>? SiteVisitsNotDone { get => Appointments?.Where(x => x.Type == AppointmentType.SiteVisit && !x.IsDone)?.ToList(); set => siteVisitsNotDone = value; }
        public bool IsSourceUpdated { get; set; }
        public UserDetails.Mobile.UserDto? SourcingManagerUser { get; set; }
        public UserDetails.Mobile.UserDto? ClosingManagerUser { get; set; }
        public UserDetails.Mobile.UserDto? BookedByUser { get; set; }
        public List<ChannelPartnerDto>? ChannelPartners { get; set; }
        public UserDetails.Mobile.UserDto? SecondaryUser { get; set; }
        public DateTime? PickedDate { get; set; }
        public bool IsPicked { get; set; }
        public DateTime? BookedDate { get; set; }
        public List<CustomFlagDto>? CustomFlags { get; set; }
        public LeadAssignmentType? AssignmentType { get; set; }
        public UserDetails.Mobile.UserDto? SecondaryFromUser { get; set; }
        public List<LinkDto>? Links { get; set; }
        public bool? IsConvertedFromData { get; set; }
        public Guid? QualifiedBy { get; set; }
        public List<LeadCallLogDto>? LeadCallLogs { get; set; }
    }
    public class BaseLeadDto : IDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? LandLine { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public string? ConfidentialNotes { get; set; }  
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public string? BookedUnderName { get; set; }
        public DateTime? BookedDate { get; set; }
        public Guid? BookedBy { get; set; }
        public string? LeadNumber { get; set; }
        public int ShareCount { get; set; }
        public string? SoldPrice { get; set; }
        public string? Rating { get; set; }
        public LeadTagDto? LeadTags { get; set; }
        public Guid? AssignTo { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public Guid? AssignedFrom { get; set; }
        public bool IsMeetingDone { get; set; }
        public Guid? MeetingLocation { get; set; }
        public bool IsSiteVisitDone { get; set; }
        public Guid? SiteLocation { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? AgencyName { get; set; }
        public List<AgencyDto>? Agencies { get; set; }
        public string? CompanyName { get; set; }
        public bool IsArchived { get; set; }
        public string? DuplicateLeadVersion { get; set; }
        public int ChildLeadsCount { get; set; }
        public Guid? ParentLeadId { get; set; }
        public Guid? ClosingManager { get; set; }
        public Guid? SourcingManager { get; set; }
        public Profession Profession { get; set; }
        public AddressDto? Address { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerExecutiveName { get; set; }
        public string? ChannelPartnerContactNo { get; set; }
        public string? Designation { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public BulkType? BulkCategory { get; set; }
        public Guid? SecondaryFromUserId { get; set; }
        public IDictionary<string, string>? AdditionalProperties { get; set; }
        public string? SerialNumber {  get; set; }
        public Guid? ArchivedBy { get; set; }
        public string? ReferralEmail { get; set; }
        public string? Nationality { get; set; }
        public List<CampaignDto>? Campaigns { get; set; }
        public PossesionType? PossesionType { get; set; }
        public string? CountryCode { get; set; }
        public string? AltCountryCode { get; set; }
        public Gender? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public MaritalStatusType? MaritalStatus { get; set; }
        public DateTime? AppointmentDoneOn { get; set; }
    }
}
