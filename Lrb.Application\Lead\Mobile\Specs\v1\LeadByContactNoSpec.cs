﻿namespace Lrb.Application.Lead.Mobile
{
    public class LeadByContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public LeadByContactNoSpec(List<string> contactNos) =>
            Query.Where(i => !i.IsDeleted && !i.IsArchived && contactNos.Contains(i.ContactNo ?? string.Empty)).Where(i => !i.IsArchived);

    }
    public class CheckLeadByContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public CheckLeadByContactNoSpec(string contactNo)
        {
            Query.Where(i => !i.IsDeleted
                && !i.IsArchived
                && ((i.ContactNo != null && (i.ContactNo.Contains(contactNo)))
                        ||
                   (i.AlternateContactNo != null && (i.AlternateContactNo.Contains(contactNo)))
                   ));
        }
        public CheckLeadByContactNoSpec(string contactNo, string countryCode)
        {
            if(contactNo != null && countryCode != null)
            {
                string formattedContactNo = contactNo.StartsWith(countryCode) ? contactNo.Substring(countryCode.Length) : countryCode + contactNo;

                Query.Where(i => !i.IsDeleted
                    && !i.IsArchived
                    && ((i.ContactNo != null && (i.ContactNo == contactNo || i.ContactNo == formattedContactNo))
                            ||
                            (i.AlternateContactNo != null && (i.AlternateContactNo == contactNo || i.AlternateContactNo == formattedContactNo))
                       ));
            }
        }
    }
}
