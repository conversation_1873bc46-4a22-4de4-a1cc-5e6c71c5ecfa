﻿using Lrb.Application.Property.Web;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web
{
    public class CreateBulkLeadDto : IDto
    {
        public CreateBulkLeadEnquiryDto? Enquiry { get; set; } = new();
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public string? BookedUnderName { get; set; }
        public string? LeadNumber { get; set; }
        public int ShareCount { get; set; }
        public string? SoldPrice { get; set; }
        public string? Rating { get; set; }
        public BulkLeadTagDto? LeadTags { get; set; }
        public Guid? AssignTo { get; set; }
        public string? Project { get; set; }
        public string? Property { get; set; }
        public List<Guid>? PropertyIds { get; set; }
        public List<Guid>? ProjectIds { get; set; }
        public virtual Guid CreatedBy { get; set; }
        public virtual Guid LastModifiedBy { get; set; }
        public DateTime CreatedOn { get; private set; }
        public DateTime? LastModifiedOn { get; set; }
    }

    public class BulkLeadTagDto : IDto
    {
        public Guid Id { get; set; }
        public bool IsHighlighted { get; set; }
        public bool IsEscalated { get; set; }
        public bool IsAboutToConvert { get; set; }
        public bool IsHotLead { get; set; }
        public bool IsIntegrationLead { get; set; }
    }

    public class CreateBulkLeadEnquiryDto : BaseBulkLeadEnquiryDto
    {
        public Guid PropertyTypeId { get; set; }
        public List<Guid>? PropertyTypeIds { get; set; }

    }

    public class BaseBulkLeadEnquiryDto : IDto
    {
        public Guid Id { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public SaleType? SaleType { get; set; }
        public LeadSource LeadSource { get; set; }
        public long? LowerBudget { get; set; }
        public long? UpperBudget { get; set; }
        public double NoOfBHK { get; set; }
        public BHKType BHKType { get; set; }
        public double Area { get; set; }
        public Guid AreaUnitId { get; set; }
        public string? AreaUnit { get; set; }
        public bool IsPrimary { get; set; }
        public BulkAddressDto? Address { get; set; }
        public List<BulkAddressDto> Addresses { get; set; }
    }

    public class BulkAddressDto : IDto
    {
        public Guid Id { get; set; }
        public string? PlaceId { get; set; }
        public string? SubLocality { get; set; }
        public string? Locality { get; set; }
        public string? District { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? Longitude { get; set; }
        public string? Latitude { get; set; }
        public bool IsGoogleMapLocation { get; set; }
    }
    public class PropertyTypeInfo
    {
        public MasterPropertyType PropertyType { get; set; }
        public List<MasterPropertyType>? PropertyTypes { get; set; }

        public BHKType BHKType { get; set; }
        public List<BHKType> BHKTypes { get; set; }
        public double NoOfBHK { get; set; }
        public List<double> BHKs { get; set; }
        public string? InvalidBHKType { get; set; }
        public string? InvalidNoOfBHK { get; set; }
        public bool IsValidInfo { get; set; }
        public string BasePropertyType { get; set; }
        public string SubPropertyType { get; set; }
        public List<string>? SubPropertyTypes { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public FurnishStatus? Furnished { get; set; }
        public List<string>? Floors{get;set;}


    }
    public class EnquiryForInfo
    {
        public EnquiryType EnquiredFor { get; set; }
        public List<EnquiryType> EnquiryTypes { get; set; }
        public string InvalidEnquiredFor { get; set; }
        public bool IsValidInfo { get; set; }
    }
    public class LeadSourceInfo
    {
        public LeadSource LeadSource { get; set; }
        public string InvalidLeadSource { get; set; }
        public bool IsValidInfo { get; set; }
    }
    public class BudgetInfo
    {
        public long Budget { get; set; }
        public bool IsValidInfo { get; set; }
        public string Invalidbudget { get; set; }
    }
    public class SubSourceInfo
    {
        public string SubSource { get; set; }
        public bool IsValidInfo { get; set; }
    }
    public class OfferTypeInfo
    {
       
            public OfferType OfferingType { get; set; }
        
            public string InvalidOfferingType { get; set; }
            public bool IsValidInfo { get; set; }
        
    }
    public class PurPoseType
    {

        public Purpose Purpose { get; set; }

        public string? InvalidOfferingType { get; set; }
        public bool IsValidInfo { get; set; }

    }
    public class ProfessionType
    {

        public Profession Profession { get; set; }
        public string? InvalidOfferingType { get; set; }
        public bool IsValidInfo { get; set; }

    }
    public class PossesionTypes
    {
        public PossesionType PossesionType { get; set; }
        public string? InvalidPossesionType { get; set; }
        public bool IsValidInfo { get; set; }

    }
    public class GenderType
    {
        public Gender Gender { get; set; }
        public string? InvalidGenderType { get; set; }
        public bool IsValidInfo { get; set; }
    }
    public class MaritalStatus
    {
        public MaritalStatusType MaritalStatusType { get; set; }
        public string? InvalidMaritalStatusType { get; set; }
        public bool IsValidInfo { get; set; }
    }
    public class DateOfBirthType
    {
        public DateTime? DateOfBirth { get; set; }
        public string? InvalidDateOfBirth { get; set; }
        public bool IsValidInfo { get; set; }
    }
}
