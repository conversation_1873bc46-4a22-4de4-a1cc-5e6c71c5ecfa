﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web
{
    public class DuplicateLeadAssigmentResponseDto
    {
        public DuplicateAssigmentUserDto? User { get; set; } = new();
        public List<DuplicateAssigmentLeadDto>? Leads { get; set; } = new();

    }
    public class DuplicateAssigmentLeadDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? ContactNo { get; set; }
    }
    public class DuplicateAssigmentUserDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
    }
    public class CountDto
    {
        public int TotalDuplicateCount { get; set; }
        public int UnAssignedCount { get; set; }
        public int UsersCount { get; set; }
        public int AssignedUsersCount { get; set; }
    }

}
