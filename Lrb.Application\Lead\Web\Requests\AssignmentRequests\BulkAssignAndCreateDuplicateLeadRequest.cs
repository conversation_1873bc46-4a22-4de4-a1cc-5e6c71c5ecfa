﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.CommonHandler;
using Lrb.Application.Reports.Web;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;

namespace Lrb.Application.Lead.Web.Requests.AssignmentRequests
{
    public class BulkAssignAndCreateDuplicateLeadRequest : BulkAssignmetDto, IRequest<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>>
    {
    }
    public class BulkAssignAndCreateDuplicateLeadRequestHandler : Bulk<PERSON><PERSON><PERSON><PERSON>and<PERSON>, IRequestHandler<BulkAssignAndCreateDuplicateLeadRequest, PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>>
    {
        private readonly IRepositoryWithEvents<BulkCommonTracker> _bulkCommonTracker;
        protected readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        public BulkAssignAndCreateDuplicateLeadRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<BulkCommonTracker> bulkCommonTracker,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo) : base(serviceProvider, typeof(BulkAssignAndCreateDuplicateLeadRequest).Name, "Handle")
        {


            _bulkCommonTracker = bulkCommonTracker;
            _duplicateInfoRepo = duplicateInfoRepo;

        }
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> Handle(BulkAssignAndCreateDuplicateLeadRequest request, CancellationToken cancellationToken)
        {


            // BulkCommonTracker? tracker = null;
            try
            {
                var featureInfo = await _duplicateInfoRepo.ListAsync(cancellationToken);

                if (!featureInfo?.Any() ?? false)
                {
                    throw new Exception("Your organization does not have this lead feature enabled.");
                }

                List<Domain.Entities.Project>? projects = new List<Domain.Entities.Project>();
                Domain.Entities.GlobalSettings? globalSetting = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                if (request.UpdateProject && request.Projects != null && request.Projects.Any())
                {
                    projects = await GetAllProjectsAsync(request.Projects, cancellationToken,globalSetting:globalSetting);
                }
                var currentUserId = request.CurrentUserId ?? _currentUserService.GetUserId();
                if (request?.LeadIds != null && request.LeadIds.Any())
                {
                    int skippedLeadCount = 0;
                    int totalLeadCount = request.LeadIds.Count();
                    var unAssignedCount = 0;
                    List<DuplicateLeadAssigmentResponseDto>? responseDtos = new();
                    List<Domain.Entities.Lead>? Leads = new List<Domain.Entities.Lead>();
                  
                        var leads = await _leadRepo.ListAsync(new LeadsByIdsSpec(request.LeadIds ?? new()));
                        var assignedUserIds = leads.Select(i => i.AssignTo.ToString()).ToList();
                        var assignedUsers = await _userService.GetListOfUsersByIdsAsync(assignedUserIds, cancellationToken);
     
                        if (leads != null && leads.Any())
                        {
                                    var duplicateLeadsInfo = await CreateDuplicateLeadsAsync(leads, request, request.Users, cancellationToken, projects, currentUserId, request.AdminDetails, assignedUsers,globalSetting:globalSetting);
                                    responseDtos = duplicateLeadsInfo.SkippedLeadsDtos;
                                    skippedLeadCount = duplicateLeadsInfo.SkippedLeadsDtos.Any() ? duplicateLeadsInfo.SkippedLeadsDtos.Count() : 0;
                                    responseDtos.RemoveAll(i => i.Leads?.Count <= 0);

                        }                    
                    var countDetails = new CountDto
                    {
                        TotalDuplicateCount = request.Users.Count * Leads.Count,
                        UsersCount = request.Users.Count,
                        AssignedUsersCount = totalLeadCount - skippedLeadCount,
                        UnAssignedCount = skippedLeadCount
                    };

                    return new PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>(responseDtos ?? new(), responseDtos?.Count ?? 0, countDetails);
                }
            }
            catch (Exception ex)
            {
                var tracker = await _bulkCommonTracker.GetByIdAsync(request.TrackerId, cancellationToken);
                if (tracker != null)
                {
                    tracker.Message = ex.Message;
                    await _bulkCommonTracker.UpdateAsync(tracker);
                }
                await AddLrbErrorAsync(ex, $"{typeof(BulkAssignAndCreateDuplicateLeadRequest).Name} - Handle()");
                throw;
            }
            return new();
        }



    }
}
