﻿namespace Lrb.Application.Lead.Web.Requests.BulkUploadTracker
{
    public class GetAllBulkMigrateTrackersRequest : PaginationFilter, IRequest<PagedResponse<LeadMigrateTracker, string>>
    {
    }
    public class GetAllBulkMigrateTrackersRequestHandler : IRequestHandler<GetAllBulkMigrateTrackersRequest, PagedResponse<LeadMigrateTracker, string>>
    {
        private readonly IRepositoryWithEvents<LeadMigrateTracker> _trackerRepo;

        public GetAllBulkMigrateTrackersRequestHandler(IRepositoryWithEvents<LeadMigrateTracker> trackerRepo)
        {
            _trackerRepo = trackerRepo;
        }
        public async Task<PagedResponse<LeadMigrateTracker, string>> Handle(GetAllBulkMigrateTrackersRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _trackerRepo.ListAsync(new LeadMigrateTrackerSpec(request), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(cancellationToken);
            return new(trackers, totalCount);
        }
    }

    public class LeadMigrateTrackerSpec : EntitiesByPaginationFilterSpec<LeadMigrateTracker>
    {
        public LeadMigrateTrackerSpec(GetAllBulkMigrateTrackersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
}
