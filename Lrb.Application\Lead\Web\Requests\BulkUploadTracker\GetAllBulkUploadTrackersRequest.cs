﻿namespace Lrb.Application.Lead.Web.Requests.BulkUploadTracker
{
    public class GetAllBulkUploadTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkLeadUploadTracker, string>>
    {
    }
    public class GetAllBulkUploadTrackersRequestHandler : IRequestHandler<GetAllBulkUploadTrackersRequest, PagedResponse<BulkLeadUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkLeadUploadTracker> _trackerRepo;
    
        public GetAllBulkUploadTrackersRequestHandler(IRepositoryWithEvents<BulkLeadUploadTracker> trackerRepo)
        {
            _trackerRepo = trackerRepo;
        }
        public async Task<PagedResponse<BulkLeadUploadTracker, string>> Handle(GetAllBulkUploadTrackersRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _trackerRepo.ListAsync(new BulkLeadUploadSpec(request), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(cancellationToken);
            return new(trackers, totalCount);
        }
    }

    public class BulkLeadUploadSpec : EntitiesByPaginationFilterSpec<BulkLeadUploadTracker>
    {
        public BulkLeadUploadSpec(GetAllBulkUploadTrackersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }

}
