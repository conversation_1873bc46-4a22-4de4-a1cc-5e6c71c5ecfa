﻿using Lrb.Application.ExportTemplate;
using Lrb.Application.Identity.Users;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web
{
    public class GetAllExportLeadTracker : PaginationFilter, IRequest<PagedResponse<ExportLeadTrackerDto, string>>
    {
    }
    public class GetAllExportLeadTrackerHandler : IRe<PERSON>Handler<GetAllExportLeadTracker, PagedResponse<ExportLeadTrackerDto, string>>
    {
        private readonly IReadRepository<ExportLeadTracker> _exportLeadTrackerRepo;
        public readonly IUserService _userService;
        public GetAllExportLeadTrackerHandler(IReadRepository<ExportLeadTracker> exportLeadTrackerRepo,
            IUserService userService)
        {
            _exportLeadTrackerRepo = exportLeadTrackerRepo;
            _userService = userService;
        }

        public async Task<PagedResponse<ExportLeadTrackerDto, string>> Handle(GetAllExportLeadTracker request, CancellationToken cancellationToken)
        {
            var leadTrackers = await _exportLeadTrackerRepo.ListAsync(new GetExportLeadTrackersSpec(request), cancellationToken);
            var totalCount = await _exportLeadTrackerRepo.CountAsync(new GetExportLeadTrackersCountSpec(), cancellationToken);
            var leadTrackerDto = leadTrackers.Adapt<List<ExportLeadTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(leadTrackerDto.Select(i => i.CreatedBy.ToString()).ToList(), cancellationToken);
            foreach (var leadTracker in leadTrackerDto)
            {
                leadTracker.ExportTemplate = (JsonConvert.DeserializeObject<ViewExportTemplateDto>(leadTracker?.Template ?? string.Empty) ?? null);
                leadTracker.ExportedUser = users?.FirstOrDefault(i => leadTracker.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }
            return new(leadTrackerDto, totalCount);
        }
    }
}
