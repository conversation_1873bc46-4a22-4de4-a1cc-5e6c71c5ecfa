﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Lead.Web
{
    public class GetAllLeadsByCustomFiltersRequest : GetAllLeadsParametersNewFilters, IRequest<PagedResponse<ViewLeadDto, string>>
    {
    }
    public class GetAllLeadsByCustomFiltersRequestHandler : IRequestHandler<GetAllLeadsByCustomFiltersRequest, PagedResponse<ViewLeadDto, string>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<CustomFilter> _repo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _masterLeadStatus;
        public GetAllLeadsByCustomFiltersRequestHandler(
            ICurrentUser currentUser,
            ILeadRepository efLeadRepository,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<CustomFilter> repo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails,
            IRepositoryWithEvents<CustomMasterLeadStatus> masterLeadStatus)
        {
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _repo = repo;
            _userDetails = userDetails;
            _masterLeadStatus = masterLeadStatus;
        }

        public async Task<PagedResponse<ViewLeadDto, string>> Handle(GetAllLeadsByCustomFiltersRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = new List<Guid>();
            (List<AppointmentType>, List<bool>) appointments = new();
            CustomFilter? filter = null;
            var isAdmin = await _dapperRepository.IsAdminV2Async(userId, tenantId ?? string.Empty);
            if (request.IsGenManagerWithTeam ?? false && (request.GeneralManagerIds?.Any() ?? false))
            {
                var generalManagerIds = await _dapperRepository.GeneralManagerAsync(request.GeneralManagerIds ?? new(), tenantId ?? string.Empty);
                if (generalManagerIds?.Any() ?? false)
                {
                    var subordinateIds = await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(generalManagerIds.ToList(), tenantId ?? string.Empty);
                    if (request.AssignTo == null)
                    {
                        request.AssignTo = new List<Guid>();
                    }
                    request.AssignTo.AddRange(subordinateIds);
                    request.AssignTo.AddRange(generalManagerIds);
                    request.AssignTo.AddRange(request.GeneralManagerIds);
                }
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(request.GeneralManagerIds);
            }
            else if ((request.GeneralManagerIds?.Any() ?? false))
            {
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(request.GeneralManagerIds);

            }
            if (request?.DesignationsId?.Any() ?? false)
            {
                var users = await _userDetails.ListAsync(new Lrb.Application.Dashboard.Web.Specs.GetUsersByDesignationIdSpec(request.DesignationsId));
                var userIds = users.Select(i => i.UserId).ToList();
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(userIds);

            }

            var tasks = new Task[]
            {
        Task.Run(async () => request.IsDualOwnershipEnabled = request.IsDualOwnershipEnabled == null ? await _dapperRepository.V2GetDualOwnershipDetails(tenantId ?? string.Empty) : request.IsDualOwnershipEnabled),
        Task.Run(async () => subIds = await GetSubordinateIdsAsync(request.Adapt<GetAllLeadsOnlyByNewFiltersRequest>(), userId, tenantId ?? string.Empty,isAdmin)),
        Task.Run(async () => appointments = await GetAppointmentTypes(request.Adapt<GetAllLeadsOnlyByNewFiltersRequest>())),
        Task.Run(async () =>
        {
            if (request.CustomFilterId == null || request.CustomFilterId == default)
            {
                filter = await _repo.FirstOrDefaultAsync(new GetCustomFiltersSpec(userId, isAdmin, true), cancellationToken);
            }
            else
            {
                filter = await _repo.FirstOrDefaultAsync(new GetCustomFiltersSpec(request.CustomFilterId ?? Guid.Empty), cancellationToken);
            }
        }),
            };
            await Task.WhenAll(tasks);

            List<Domain.Entities.Lead>? leads = null;
            int totalCount = default;
            List<CustomMasterLeadStatus> customStatus = null;
            try
            {
                if ((request?.PropertyToSearch?.Any() ?? false) && !string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch.Contains("Status"))
                {
                    customStatus = await _masterLeadStatus.ListAsync(new Lrb.Application.CustomStatus.Web.GetAllStatusByNameSpec(request.SearchByNameOrNumber ?? string.Empty, false), cancellationToken);
                }
            }catch(Exception ex){ }
            var tasks1 = new Task[]
            {
         Task.Run(async () => leads = (await _efLeadRepository.GetAllLeadsByCustomFiltersForWebAsync(filter ?? new CustomFilter(), request.Adapt<GetAllLeadsParametersNewFilters>(), subIds, userId, isAdmin, new List<Guid>(),customStatus)).ToList()),
        Task.Run(async () => totalCount = await _efLeadRepository.GetLeadsCountByCustomFiltersForWebAsync(filter ?? new CustomFilter(), request.Adapt<GetAllLeadsParametersNewFilters>(), subIds, userId, isAdmin, new List<Guid>(),customStatus)),
            };
            await Task.WhenAll(tasks1);

            leads?.ForEach(lead => FilterAppointments(request.Adapt<GetAllLeadsOnlyByNewFiltersRequest>(), isAdmin, userId, lead, appointments.Item1 ?? new(), appointments.Item2 ?? new(), subIds));
            List<ViewLeadDto> leadDtos = leads?.Adapt<List<ViewLeadDto>>() ?? new();
            return new(leadDtos, totalCount);
        }
        private async Task<List<Guid>> GetSubordinateIdsAsync(GetAllLeadsOnlyByNewFiltersRequest request, Guid userId, string tenantId, bool isAdmin)
        {

            var assignToIds = request?.AssignTo ?? new List<Guid>();
            return assignToIds.Any()
                ? (request?.IsWithTeam ?? false)
                    ? (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesV2Async(assignToIds, tenantId ?? string.Empty)).ToList()
                    : assignToIds
                : (await _dapperRepository.GetSubordinateIdsV2Async(userId, tenantId ?? string.Empty, request.CanAccessAllLeads, isAdmin))?.ToList() ?? new List<Guid>();
        }
        public async Task<(List<AppointmentType>, List<bool>)> GetAppointmentTypes(GetAllLeadsOnlyByNewFiltersRequest request)
        {
            List<AppointmentType> appTypes = new();
            List<bool> appDoneStatuses = new();
            if (request.MeetingOrVisitStatuses == null)
            {
                return (appTypes, appDoneStatuses);
            }
            request.MeetingOrVisitStatuses?.ForEach(appType =>
            {
                switch (appType)
                {
                    case MeetingOrVisitCompletionStatus.IsMeetingDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsMeetingNotDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(false);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitNotDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(false);
                        break;
                }
            });
            appTypes = appTypes.Distinct().ToList();
            appDoneStatuses = appDoneStatuses.Distinct().ToList();
            return (appTypes, appDoneStatuses);
        }
        private void FilterAppointments(GetAllLeadsOnlyByNewFiltersRequest request, bool isAdmin, Guid userId, Domain.Entities.Lead lead, List<AppointmentType> appTypes, List<bool> appDoneStatuses,List<Guid> subIds)
        {
            if (lead?.Appointments?.Any() ?? false)
            {
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    var uniqueAppointments = lead.Appointments.Where(i => i.UniqueKey != null && i.UniqueKey != Guid.Empty).DistinctBy(i => i.UniqueKey).ToList();
                    uniqueAppointments.AddRange(lead.Appointments.Where(i => i.UniqueKey == null || i.UniqueKey == Guid.Empty).ToList());
                    lead.Appointments = uniqueAppointments.Where(i => request.AppointmentDoneByUserIds.Contains(i.CreatedBy) && appTypes.Contains(i.Type) && appDoneStatuses.Contains(i.IsDone)).ToList();
                }
                else if (subIds != null && !(isAdmin))
                {
                    if ((lead?.Appointments?.Any() ?? false) && lead.Appointments.Any(i => subIds.Contains(i.UserId)))
                    {
                        var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default).ToList() ?? new();

                        var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default && subIds.Contains(i.UserId))?.DistinctBy(i => i.UniqueKey)?.OrderBy(i => i.LastModifiedOn)?.ToList() ?? new();

                        appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                        lead.Appointments = appointmentsWithoutUniqueKey;
                    }
                    else
                    {
                        lead.Appointments = null;
                    }
                }
                else
                {
                    var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default)?.ToList() ?? new();
                    var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();
                    appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                    lead.Appointments = appointmentsWithoutUniqueKey;
                }
            }
        }
    }
}
