﻿using Lrb.Application.Lead.Web.Specs;

namespace Lrb.Application.Lead.Web
{
    public class GetAllBulkOperationCommonTrackerRequest : PaginationFilter, IRequest<PagedResponse<BulkOperationCommonTrackerDto, string>>
    {
        public string? Type { get; set; }
    }

    public class GetAllBulkOperationCommonTrackerRequestHandler : IRequestHandler<GetAllBulkOperationCommonTrackerRequest, PagedResponse<BulkOperationCommonTrackerDto, string>>
    {
        private readonly IRepositoryWithEvents<BulkCommonTracker> _commonTrackerRepo;
        public GetAllBulkOperationCommonTrackerRequestHandler(IRepositoryWithEvents<BulkCommonTracker> commonTrackerRepo)
        {
            _commonTrackerRepo = commonTrackerRepo;
        }
        public async Task<PagedResponse<BulkOperationCommonTrackerDto, string>> Handle(GetAllBulkOperationCommonTrackerRequest request, CancellationToken cancellationToken)
        {
            var allLeadTrackers = await _commonTrackerRepo.ListAsync(new GetAllBulkOperationCommonTrackerSpecs(request), cancellationToken);
            var count = await _commonTrackerRepo.CountAsync(new GetAllBulkOperationCommonTrackerCountSpecs(request.Type));
            var allLeadtrackerDtos = allLeadTrackers.Adapt<List<BulkOperationCommonTrackerDto>>();
            return new(allLeadtrackerDtos, count);
        }
    }
}
