﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Domain.Entities.MasterData;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Project.Web;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Team.Web;
namespace Lrb.Application.Lead.Web
{
    public class GetAllLeadsAnonymousRequest : GetAllLeadsParametersNewFilters, IRequest<PagedResponse<PullViewLeadDto, string>>
    {

    }
    public class GetAllLeadsAnonymousRequestHandler : GetAll<PERSON>eadsCommonHandler, IRequestHandler<GetAllLeadsAnonymousRequest, PagedResponse<PullViewLeadDto, string>>
    {
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        private readonly IUserService _userService;

        public GetAllLeadsAnonymousRequestHandler(
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> masterLeadStatus,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepository,
            IUserService userService)
            : base(currentUser, dapperRepository, efLeadRepository, masterLeadStatus)
        {
            _leadRepositoryAsync = leadRepositoryAsync;
            _userDetails = userDetails;
            _globalSettingsRepository = globalSettingsRepository;
            _userService = userService;
        }

        public async Task<PagedResponse<PullViewLeadDto, string>> Handle(GetAllLeadsAnonymousRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            if (globalSettings?.CanAccessAnonymousApis != true)
            {
                throw new UnauthorizedAccessException("Access to Leads is restricted.");
            }
            List<Guid> subIds = new();
            //  subIds = request?.AssignTo ?? new List<Guid>();
            var customStatus = await _customMasterLeadStatusRepo.ListAsync(cancellationToken);
            List<Guid> leadHistoryIds = new();
            GetAllLeadsAnonymousRequest newRequest = request.Adapt<GetAllLeadsAnonymousRequest>();
            GetAllLeadsAnonymousRequest newRequestForCount = request.Adapt<GetAllLeadsAnonymousRequest>();
            var leads = _efLeadRepository.GetAnonymousLeadsForWebAsync(newRequest, leadHistoryIds, customStatus).Result.ToList();
            List<PullViewLeadDto> leadsDtos = leads.Adapt<List<PullViewLeadDto>>();
            (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = _efLeadRepository.GetAppointmentTypes(request);
            var userIds = leadsDtos.SelectMany(i => new[] { i.AssignTo , i.AssignedFrom , i.SecondaryFromUserId, i.SecondaryUserId}).Where(id => id.HasValue && id.Value != Guid.Empty).Select(id => id.Value.ToString()).Distinct().ToList();
            var users = await _userService.GetListOfActiveandDeactiveUsersByIdsAsync(userIds , cancellationToken);

            leadsDtos.ForEach(lead =>
            {
                if (lead?.Appointments?.Any() ?? false)
                {
                    var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default).ToList() ?? new();
                    var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();

                    appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                    lead.Appointments = appointmentsWithoutUniqueKey;
                }
                var assignedUser = users?.FirstOrDefault(i => i.Id == (lead?.AssignTo ?? Guid.Empty));
                var assignedFromUser = users?.FirstOrDefault(i => i.Id == (lead?.AssignedFrom ?? Guid.Empty));
                var secondaryUser = users?.FirstOrDefault(i => i.Id == (lead?.SecondaryUserId ?? Guid.Empty));
                var secondaryFromUser = users?.FirstOrDefault(i => i.Id == (lead?.SecondaryFromUserId ?? Guid.Empty));

                if (assignedUser != null)
                {
                    lead.AssignedUser = assignedUser.Adapt<UserDto>();
                }
                if (assignedFromUser != null)
                {
                    lead.AssignedFromUser = assignedFromUser.Adapt<UserDto>();
                }
                if (secondaryUser != null)
                {
                    lead.SecondaryUser = secondaryUser.Adapt<UserDto>();
                }
                if (secondaryFromUser != null)
                {
                    lead.SecondaryFromUser = secondaryFromUser.Adapt<UserDto>();
                }
            });
            var count = leads.Count();
            var totalCount = await _efLeadRepository.GetAnonymousLeadsCountForWebAsync(newRequestForCount, leadHistoryIds, customStatus);
            return new(leadsDtos, totalCount);
        }
    }
}
