﻿namespace Lrb.Application.Lead.Web
{
    public class GetLeadCommunicationsRequest : IRequest<Response<Dictionary<Guid, Dictionary<ContactType, int>>>?>
    {
        public List<Guid>? LeadIds { get; set; }
    }
    public class GetLeadCommunicationsRequestHandler : IRequestHandler<GetLeadCommunicationsRequest, Response<Dictionary<Guid, Dictionary<ContactType, int>>>?>
    {
        private readonly IRepositoryWithEvents<LeadCommunication> _communicationRepo;
        private readonly ICurrentUser _currentUser;
        protected readonly IDapperRepository _dapperRepository;

        public GetLeadCommunicationsRequestHandler(IRepositoryWithEvents<LeadCommunication> communicationRepo, ICurrentUser currentUser, IDapperRepository dapperRepository)
        {
            _communicationRepo = communicationRepo;
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<Dictionary<Guid, Dictionary<ContactType, int>>>?> Handle(GetLeadCommunicationsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request?.LeadIds?.Count() <= 0)
                {
                    return new(null);
                }
                var userId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                var communications = await _dapperRepository.GetLeadsCommunicationsByLeadIds(tenantId ?? string.Empty,userId,request.LeadIds,isAdmin);
                var result = new List<CommunicationDto>();
                if (communications?.Any() ?? false)
                {
                    return new(communications
                                  .Select(communication => new
                                  {
                                      LeadId = communication.LeadId,
                                      UniqueKey = communication.UniqueKey != null ? communication.UniqueKey != Guid.Empty ? communication.UniqueKey : Guid.NewGuid() : Guid.NewGuid(),
                                      ContactType = communication.ContactType
                                  })
                                  .DistinctBy(i => i.UniqueKey)
                                  .GroupBy(group => new { group.LeadId })
                                  .Select(group => new
                                  {
                                      LeadId = group.Key.LeadId,
                                      ContactRecords = group
                                   .Select(communication => communication.ContactType)
                                   .GroupBy(contactType => contactType)
                                   .ToDictionary(contactTypeGroup => contactTypeGroup.Key, contactTypeGroup => contactTypeGroup.Count())
                                  })
                                    .ToDictionary(dto => dto.LeadId, dto => dto.ContactRecords));
                }
                else
                {
                    return new(null);
                }
            }
            catch (Exception ex) 
            {
                return new(null, ex.Message);
            }

        }
    }
}
