﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.AssignmentRequests;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Shared.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{

    public class ProcessLeadSourceUpdateRequest :IRequest<Response<bool>>
    {
        public LeadSource LeadSource { get; set; }
        public List<Guid> LeadIds { get; set; } = default!;
        public string? SubSource { get; set; }
        public Guid? TrackerId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }
        public BulkType? BulkCategory { get; set; }

    }
    public class ProcessLeadSourceUpdateRequestHandler : IRequestHandler<ProcessLeadSourceUpdateRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<BulkCommonTracker> _bulkCommonTracker;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IUserService _userService;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly ICurrentUser _currentUser;
        protected readonly IDapperRepository _dapperRepository;
        public ProcessLeadSourceUpdateRequestHandler(IServiceProvider serviceProvider,
            IRepositoryWithEvents<BulkCommonTracker> bulkCommonTracker,
            ILeadRepositoryAsync leadRepositoryAsync,
            ICurrentUser currentUser,
            IUserService userService,
            IDapperRepository dapperRepository)
        {
            _bulkCommonTracker = bulkCommonTracker;
            _leadRepositoryAsync = leadRepositoryAsync;
            _serviceProvider = serviceProvider;
            _currentUser = currentUser;
            _userService = userService;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<bool>> Handle(ProcessLeadSourceUpdateRequest request, CancellationToken cancellationToken)
        {
            BulkCommonTracker? tracker = null;
            try
            {
                int leadsPerchunk = request.LeadIds.Count > 50 ? 50 : request.LeadIds.Count;
                var currentUser = request.CurrentUserId ?? _currentUser.GetUserId();
                string tenantId = request.TenantId ?? _currentUser.GetTenant();
                var commonTracker = new BulkCommonTracker();
                if (request.LeadIds.Any() && request.LeadIds.Count >= 50)
                {
                    commonTracker = new BulkCommonTracker()
                    {
                        TotalCount = request.LeadIds.Count(),
                        Status = UploadStatus.InProgress,
                        RawJson = request.Serialize(),
                        ClassType = "BulkLeadSourceUpdate",
                        Module = "lead",
                        CreatedBy = currentUser,
                        LastModifiedBy = currentUser,
                    };
                    tracker = await _bulkCommonTracker.AddAsync(commonTracker);
                    request.TrackerId = tracker?.Id ?? default;
                }

                request.CurrentUserId = request.CurrentUserId ?? currentUser;
                var chunks = request.LeadIds.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Guid>(i));
                List<Task> tasks = new List<Task>();
                var totalLeadCount = 0;
                var totalUpdatedCount = 0;
                var skippedLeadCount = 0;
                var allSources =  (await _dapperRepository.GetAllIntegrationSubSourceAsync<SourceDto>(request.TenantId ?? _currentUser.GetTenant() ?? string.Empty)).ToList();
                foreach (var chunk in chunks.ToList())
                {
                    var newRequest = request.Adapt<UpdateBulkLeadSourceRequest>();
                    newRequest.LeadIds = chunk.ToList();
                    newRequest.Sources = allSources;
                    totalLeadCount = totalLeadCount + newRequest.LeadIds.Count();
                    var task = Task.Run(async () =>
                    {

                        using (var scope = _serviceProvider.CreateScope())
                        {
                                var mediator = scope.ServiceProvider.GetService<IMediator>();
                                var result = await mediator.Send(newRequest);
                                Interlocked.Add(ref totalUpdatedCount, result.Data);
                        }

                    });
                    tasks.Add(task);

                }
                await Task.WhenAll(tasks);
                #region Update Common Bulk Upload Tracker
                if (request.TrackerId != null && request.TrackerId != Guid.Empty)
                {
                    tracker = await _bulkCommonTracker.GetByIdAsync(request.TrackerId);
                    tracker.TotalUploadedCount = tracker.TotalUploadedCount + totalLeadCount;
                    tracker.Status = tracker.TotalUploadedCount < tracker.TotalCount ? UploadStatus.InProgress : UploadStatus.Completed;
                    tracker.DistinctCount = tracker.DistinctCount + totalUpdatedCount;
                    tracker.UpdatedCount = tracker.UpdatedCount + totalUpdatedCount;
                    tracker.DuplicateCount = tracker.DuplicateCount + (totalLeadCount - totalUpdatedCount);
                    await _bulkCommonTracker.UpdateAsync(tracker);
                }
                #endregion
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.ToString();
                    await _bulkCommonTracker.UpdateAsync(tracker);
                }
                throw new NotFoundException("No leads found by the provided lead Ids.");

            }

            return new(true);
        }

        protected async Task AddLrbErrorAsync(Exception ex, string moduleName)
        {
            try
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = moduleName
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            catch
            {
                throw;
            }
        }
    }
}
