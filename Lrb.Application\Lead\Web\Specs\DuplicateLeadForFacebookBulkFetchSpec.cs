﻿using Lrb.Application.Lead.Web.Dtos;

namespace Lrb.Application.Lead.Web.Specs
{
    public class DuplicateLeadForFacebookBulkFetchSpec : Specification<Domain.Entities.Lead>
    {
        public DuplicateLeadForFacebookBulkFetchSpec(string contactNo, Guid userId)
        {
            Query.Where(i => (i.ContactNo == contactNo || i.AlternateContactNo == contactNo) && i.AssignTo == userId);
        }
        public DuplicateLeadForFacebookBulkFetchSpec(string contactNo)
        {
            Query.Where(i => !i.IsArchived && !i.IsDeleted && (i.ContactNo != null && i.ContactNo.Contains(contactNo)) || (i.AlternateContactNo != null && i.AlternateContactNo.Contains(contactNo)));
        }
        public DuplicateLeadForFacebookBulkFetchSpec(List<string> contactNos)
        {
            Query.Where(i => !i.IsArchived && !i.IsDeleted)
                 .Where(i => (i.ContactNo != null && contactNos.Contains(i.ContactNo) || (i.AlternateContactNo != null && contactNos.Contains(i.AlternateContactNo))));
        }
        public DuplicateLeadForFacebookBulkFetchSpec(string contactNo, DateTime submittedDateTime)
        {
            Query.Where(i => !i.IsArchived && !i.IsDeleted && (i.ContactNo != null && i.ContactNo.Contains(contactNo)) || (i.AlternateContactNo != null && i.AlternateContactNo.Contains(contactNo)))
                .Where(i => (i.CreatedOnPortal != null && i.CreatedOnPortal == submittedDateTime) || i.CreatedOn == submittedDateTime || (i.CreatedOn > submittedDateTime && i.CreatedOn < submittedDateTime.AddSeconds(2)));
        }
    }

    public class DuplicateLeadsForAssignmentSpec : Specification<Domain.Entities.Lead>
    {
        public DuplicateLeadsForAssignmentSpec(string contactNo, Guid userId)
        {
            Query.Where(i => (i.ContactNo == contactNo || i.AlternateContactNo == contactNo) && (i.AssignTo == userId));
        }
        public DuplicateLeadsForAssignmentSpec(string contactNo, Guid userId, Guid secondaryUserId)
        {
            Query.Where(i => (i.ContactNo == contactNo || i.AlternateContactNo == contactNo) && i.AssignTo == userId && i.SecondaryUserId == secondaryUserId);
        }
    }
    public class GetAssignedLeadsSpec : Specification<Domain.Entities.Lead>
    {
        public GetAssignedLeadsSpec(string mobileWithoutCountryCode, string contactNo, Guid userId)
        {
            Query.Where(i => (!i.IsArchived && !i.IsDeleted && (!string.IsNullOrEmpty(i.ContactNo)) && ((i.ContactNo == contactNo || i.ContactNo == mobileWithoutCountryCode))) && (i.AssignTo == userId || i.SecondaryUserId == userId));

        }
    }
    public class GetAssignedLeadsWithoutCountryCodeSpec : Specification<Domain.Entities.Lead>
    {
        public GetAssignedLeadsWithoutCountryCodeSpec(string contactNo, Guid userId)
        {
            Query.Where(i => (!i.IsArchived && !i.IsDeleted && (!string.IsNullOrEmpty(i.ContactNo)) && (i.ContactNo.Contains(contactNo))) && (i.AssignTo == userId || i.SecondaryUserId == userId));

        }


    }
    public class TrueDuplicateLeadSpec : Specification<Domain.Entities.Lead>
    {
        public TrueDuplicateLeadSpec(IEnumerable<string> contactNos)
        {
            Query.Include(i => i.Enquiries).Where(i => !i.IsDeleted && i.ContactNo != null && contactNos.Contains(i.ContactNo));
        }
    }
    public class DuplicateFeatureSpec : Specification<Domain.Entities.Lead>
    {
        public DuplicateFeatureSpec(DuplicateLeadFeatureInfo featureInfo, DuplicateLeadSpecDto lead, List<Address>? address = null, List<string>? contactNos = null, string? mobileWithoutCountryCode = null)
        {
            featureInfo.StutusIds = featureInfo.StutusIds?.Where(i => i != null)?.ToList() ?? new();
            address = lead.Addresses;
            var Statuses = new List<string>() { "not_interested", "dropped" };
            var StatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
            Query.Where(i => !i.IsDeleted
            && !i.IsArchived
            && (
                i.ContactNo == lead.ContactNo
                || i.ContactNo == mobileWithoutCountryCode
                || (mobileWithoutCountryCode != null && (i.ContactNo.Length >= 7 && mobileWithoutCountryCode.Length >= 7)
                && i.ContactNo.EndsWith(mobileWithoutCountryCode))));

            Query.Where(i => i.CustomLeadStatus != null && (featureInfo.StutusIds != null && (!featureInfo.StutusIds.Contains(i.CustomLeadStatus.MasterLeadStatusId) || !featureInfo.StutusIds.Contains(i.CustomLeadStatus.MasterLeadStatusBaseId))))
                .Include(i => i.Projects)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries);
            if (contactNos?.Any() ?? false)
            {
                Query.Where(i => (!string.IsNullOrWhiteSpace(i.ContactNo) && contactNos.Contains(i.ContactNo)));
            }
            if (featureInfo.IsSourceBased)
            {
                Query.Where(i => i.Enquiries.Any(i => i.LeadSource == lead.LeadSource));

            }
            if (featureInfo.IsSubSourceBased && lead.SubSource != null)
            {
                Query.Where(i => i.Enquiries.Any(i => i.SubSource.ToLower() == lead.SubSource.ToLower()));
            }
            if (featureInfo.IsLocationBased && address != null)
            {
                var subLocality = address?.Select(i => i.SubLocality?.ToLower()).ToList();
                var locality = address?.Select(i => i.Locality?.ToLower()).ToList();
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Address.SubLocality.ToLower() == subLocality && i.Address.Locality.ToLower() == locality));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Addresses.Where(i => i.SubLocality != null && subLocality.Contains(i.SubLocality.ToLower())).Any() && i.Addresses.Where(i => i.Locality != null && locality.Contains(i.Locality.ToLower())).Any()));
            }
            if (featureInfo.IsProjectBased && (lead.ProjectsList?.Count == 1))
            {
                Query.Where(i => i.Projects.Select(i => i.Name.ToLower()).Any(proj => proj == lead.ProjectsList.FirstOrDefault().ToLower()));
            }
            if (featureInfo.IsProjectBased && (lead.ProjectsList?.Count > 1))
            {
                Query.Where(i => i.Projects.Select(i => i.Name.ToLower()).Any(proj => lead.ProjectsList.Select(i => i.ToLower()).Any(j => j == proj)) && lead.ProjectsList.Count() == i.Projects.Count());
            }
        }
    }
    public class GetRootLeadSpec : Specification<Domain.Entities.Lead>
    {
        public GetRootLeadSpec(string contactNo, string? altno)
        {
            Query.Where(i => !i.IsDeleted
                && !i.IsArchived
                && ((i.ContactNo != null) && ((contactNo == (i.ContactNo)) || (i.AlternateContactNo != null && (altno == (i.AlternateContactNo))) || (i.ContactNo != null && (altno == (i.ContactNo))) || (i.AlternateContactNo != null && (contactNo == (i.AlternateContactNo))))));
            Query.Where(i => i.ParentLeadId == null && i.RootId == null);
            Query.OrderBy(i => i.CreatedOn);
        }
        public GetRootLeadSpec(List<string>? contactNos)
        {
            Query.Where(i => (contactNos != null) && contactNos.Contains(i.ContactNo) && !i.IsDeleted && (i.ParentLeadId == null && i.RootId == null));

        }
        public GetRootLeadSpec(Guid id, bool? isArchived = false)
        {
            Query.Where(i => !i.IsDeleted)
                  //.Where(i => i.RootId == null)
                  .Where(i => i.Id == id)
                  .Include(i => i.TagInfo)
                 .Include(i => i.CustomLeadStatus)
                  .Include(i => i.Enquiries)
                  .Include(i => i.Enquiries)
                      //.ThenInclude(i => i.Address)
                      .ThenInclude(i => i.Addresses)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.Zone)
                  .Include(i => i.Enquiries)
                      //.ThenInclude(i => i.Address)
                      .ThenInclude(i => i.Addresses)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.City)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.PropertyType)
                  .Include(i => i.Appointments)
                      .ThenInclude(i => i.Location)
                  .Include(i => i.Projects)
                  .Include(i => i.Properties)
                  .Include(i => i.Address)
                  .Include(i => i.ChannelPartners)
                  .Include(i => i.CustomFlags)
                  .ThenInclude(i => i.Flag)
                  .Include(i => i.Enquiries)
                  .ThenInclude(i => i.PropertyTypes);
            if (isArchived == null || !isArchived.Value)
            {
                Query.Where(i => !i.IsArchived);
            }

        }
        public GetRootLeadSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted)

                 .Where(i => i.RootId == null)
                 .Where(i => i.Id == id)
                  .Include(i => i.TagInfo)
                 .Include(i => i.CustomLeadStatus)
                  .Include(i => i.Enquiries)
                  .Include(i => i.Enquiries)
                      //.ThenInclude(i => i.Address)
                      .ThenInclude(i => i.Addresses)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.Zone)
                  .Include(i => i.Enquiries)
                      //.ThenInclude(i => i.Address)
                      .ThenInclude(i => i.Addresses)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.City)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.PropertyType)
                  .Include(i => i.Appointments)
                      .ThenInclude(i => i.Location)
                  .Include(i => i.Projects)
                  .Include(i => i.Properties)
                  .Include(i => i.Address)
                  .Include(i => i.ChannelPartners)
                  .Include(i => i.CustomFlags)
                  .ThenInclude(i => i.Flag)
                  .Include(i => i.Enquiries)
                 .ThenInclude(i => i.PropertyTypes);


        }
        public GetRootLeadSpec(string contactNo, string? altno,Guid id)
        {
            Query.Where(i => !i.IsDeleted
                && !i.IsArchived
                && ((i.ContactNo != null) && ((contactNo == (i.ContactNo)) || (i.AlternateContactNo != null && (altno == (i.AlternateContactNo))) || (i.ContactNo != null && (altno == (i.ContactNo))) || (i.AlternateContactNo != null && (contactNo == (i.AlternateContactNo))))));
            Query.Where(i => i.ParentLeadId == null && i.RootId == null && (i.Id != id));
        }
    }
    public class GetContcactNoSpec : Specification<Domain.Entities.Lead>
    {
        public GetContcactNoSpec(string contactNo, string? alternateNo)
        {
            //Query.Where(i => i.ContactNo.Contains(contactNo) ||( i.AlternateContactNo.Contains(contactNo)) || (i.ContactNo.Contains(alternateNo)) || (i.AlternateContactNo.Contains(alternateNo)) && !i.IsDeleted);
            Query.Where(i => !i.IsDeleted && !i.IsArchived && ((i.ContactNo != null && (contactNo == (i.ContactNo))) || (i.AlternateContactNo != null && (alternateNo == (i.AlternateContactNo))) || (i.ContactNo != null && (alternateNo == (i.ContactNo))) || (i.AlternateContactNo != null && (contactNo == (i.AlternateContactNo)))));
            Query.Where(i => i.ParentLeadId == null && i.RootId == null);
        }

    }

    public class GetRootDataSpec : Specification<Domain.Entities.Prospect>
    {
        public GetRootDataSpec(string contactNo)
        {
            Query.Where(i => i.ContactNo.Contains(contactNo) && !i.IsDeleted);
        }
        public GetRootDataSpec(Guid id, bool? isArchived = false)
        {
            Query.Where(i => !i.IsDeleted)
                  //.Where(i => i.RootId == null)
                  .Where(i => i.Id == id)

                 .Include(i => i.Status)
                  .Include(i => i.Enquiries)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.Address)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.Zone)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.Address)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.City)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.PropertyType)
                  .Include(i => i.Address)
                      .ThenInclude(i => i.Location)
                  .Include(i => i.Projects)
                  .Include(i => i.Properties)
                  .Include(i => i.Address)
                  .Include(i => i.ChannelPartners)
                  .Include(i => i.Enquiries)
                  .ThenInclude(i => i.PropertyTypes);
            if (isArchived == null || !isArchived.Value)
            {
                Query.Where(i => !i.IsArchived);
            }

        }
        public GetRootDataSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted)


                 .Where(i => i.Id == id)

                 .Include(i => i.Status)
                  .Include(i => i.Enquiries)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.Address)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.Zone)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.Address)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.City)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.PropertyType)
                  .Include(i => i.Address)
                      .ThenInclude(i => i.Location)
                  .Include(i => i.Projects)
                  .Include(i => i.Properties)
                  .Include(i => i.Address)
                  .Include(i => i.ChannelPartners)
                  .Include(i => i.Enquiries)
                  .ThenInclude(i => i.PropertyTypes);

        }

    }


    public class GetDuplicateLeadSpec : Specification<Domain.Entities.Lead>
    {
        public GetDuplicateLeadSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted)

                 .Where(i => i.RootId == id || i.ParentLeadId == id)
                  .Include(i => i.TagInfo)
                 .Include(i => i.CustomLeadStatus)
                  .Include(i => i.Enquiries)
                  .Include(i => i.Enquiries)
                      //.ThenInclude(i => i.Address)
                      .ThenInclude(i => i.Addresses)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.Zone)
                  .Include(i => i.Enquiries)
                      //.ThenInclude(i => i.Address)
                      .ThenInclude(i => i.Addresses)
                          .ThenInclude(i => i.Location)
                              .ThenInclude(i => i.City)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.PropertyType)
                  .Include(i => i.Appointments)
                      .ThenInclude(i => i.Location)
                  .Include(i => i.Projects)
                  .Include(i => i.Properties)
                  .Include(i => i.Address)
                  .Include(i => i.ChannelPartners)
                  .Include(i => i.CustomFlags)
                  .ThenInclude(i => i.Flag)
                  .Include(i => i.Enquiries)
                  .ThenInclude(i => i.PropertyTypes);
        }
    }
    public class GetFeatureInfoSpec : Specification<DuplicateLeadFeatureInfo>
    {
        public GetFeatureInfoSpec()
        {
        }
    }

}
