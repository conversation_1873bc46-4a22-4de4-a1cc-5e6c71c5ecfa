﻿namespace Lrb.Application.Lead.Web.Specs
{
    public class GetAllBulkOperationCommonTrackerSpecs : EntitiesByPaginationFilterSpec<BulkCommonTracker>
    {
        public GetAllBulkOperationCommonTrackerSpecs(GetAllBulkOperationCommonTrackerRequest request) :  base(request) 
        {
            Query.Where(i => !i.IsDeleted && request.Type != null &&  i.Module == request.Type.ToLower().Trim())
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetAllBulkOperationCommonTrackerCountSpecs :  Specification<BulkCommonTracker>
    {
        public GetAllBulkOperationCommonTrackerCountSpecs(string? type)
        {
            Query.Where(i => !i.IsDeleted && type != null &&  i.Module == type.ToLower().Trim() );
        }
    }
}
