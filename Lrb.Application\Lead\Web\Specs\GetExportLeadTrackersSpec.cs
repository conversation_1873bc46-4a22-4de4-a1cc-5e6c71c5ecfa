﻿namespace Lrb.Application.Lead.Web
{
    public class GetExportLeadTrackersSpec : EntitiesByPaginationFilterSpec<ExportLeadTracker>
    {
        public GetExportLeadTrackersSpec(GetAllExportLeadTracker filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted).OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetExportLeadTrackersCountSpec : Specification<ExportLeadTracker>
    {
        public GetExportLeadTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}
