﻿using Lrb.Application.Lead.Web.Requests.GetRequests;

namespace Lrb.Application.Lead.Web
{
    public class LeadByContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public LeadByContactNoSpec(List<string> contactNos)
        {
            contactNos = contactNos.ToList();
            Query.Where(i => !i.IsDeleted
                                && !i.IsArchived
                                && (
                                    (!string.IsNullOrWhiteSpace(i.ContactNo) && contactNos.Contains(i.ContactNo))
                                    || (!string.IsNullOrWhiteSpace(i.AlternateContactNo) && contactNos.Contains(i.AlternateContactNo))
                                   )
                             );
        }

        public LeadByContactNoSpec(string contactNo) =>
           Query.Where(i => !i.IsDeleted
               && !i.IsArchived
               && ((!string.IsNullOrWhiteSpace(i.ContactNo) && i.ContactNo.Contains(contactNo))
                   || (!string.IsNullOrWhiteSpace(i.AlternateContactNo) && i.AlternateContactNo.Contains(contactNo))))
           .Include(i => i.CustomLeadStatus)
           .Include(i => i.Enquiries)
               .ThenInclude(i => i.Addresses)
                   .ThenInclude(i => i.Location)
           .Include(i => i.Enquiries)
               .ThenInclude(i => i.PropertyType)
           .Include(i => i.Enquiries)
               .ThenInclude(i => i.PropertyTypes)
           .Include(i => i.Projects)
           .Include(i => i.Properties)
               .ThenInclude(i => i.Dimension);

        public LeadByContactNoSpec(string contactNo, string mobileWithoutCountryCode) =>
        Query.Where(i => !i.IsDeleted
            && !i.IsArchived
            && ((!string.IsNullOrWhiteSpace(i.ContactNo)
                    && (i.ContactNo == contactNo || i.ContactNo == mobileWithoutCountryCode))
                ||
                (!string.IsNullOrWhiteSpace(i.AlternateContactNo)
                    && (i.AlternateContactNo == contactNo || i.AlternateContactNo == mobileWithoutCountryCode))
                || (mobileWithoutCountryCode != null && (i.ContactNo.Length >= 7 && mobileWithoutCountryCode.Length >= 7)
                && i.ContactNo.EndsWith(mobileWithoutCountryCode))
               )
        ).Include(i => i.CustomLeadStatus);
    }

    public class CheckLeadByContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public CheckLeadByContactNoSpec(string contactNo, string countryCode)
        {
            string formattedContactNo = contactNo.StartsWith(countryCode) ? contactNo.Substring(countryCode.Length) : contactNo;

            Query.Where(i => !i.IsDeleted
                && !i.IsArchived
                && ((i.ContactNo != null && (i.ContactNo.Contains(contactNo) || i.ContactNo.Contains(formattedContactNo)))
                        ||
                        (i.AlternateContactNo != null && (i.AlternateContactNo.Contains(contactNo) || i.AlternateContactNo.Contains(formattedContactNo)))
                   ));
        }
        public CheckLeadByContactNoSpec(List<string> contactNos, List<string>? alternateNos)
        {
            Query.Where(i => !i.IsDeleted
                && !i.IsArchived
                && ((i.ContactNo != null && (contactNos.Contains(i.ContactNo))) || (i.AlternateContactNo != null && (alternateNos != null && alternateNos.Any() && alternateNos.Contains(i.AlternateContactNo))) || (i.ContactNo != null && (alternateNos != null && alternateNos.Any() && alternateNos.Contains(i.ContactNo))) || (i.AlternateContactNo != null && (contactNos.Contains(i.AlternateContactNo)))));
        }

    }
    public class CheckLeadByPrimaryContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public CheckLeadByPrimaryContactNoSpec(List<string> contactNos)
        {
            Query.Where(i => !i.IsDeleted
                && !i.IsArchived
                && (i.ContactNo != null && contactNos.Contains(i.ContactNo)));
        }
    }

    public class LeadByContactNoAndSourceSpec : Specification<Domain.Entities.Lead>
    {
        public LeadByContactNoAndSourceSpec(List<string> contactNos, LeadSource leadSource, string subSource)
        {
            Query.Include(i => i.Enquiries)
                      .Where(i => !i.IsDeleted
                                  && !string.IsNullOrWhiteSpace(i.ContactNo)
                                  && contactNos.Contains(i.ContactNo))
                 .Where(i => i.Enquiries.Any(i => i.LeadSource == leadSource && !string.IsNullOrWhiteSpace(i.SubSource) && i.SubSource.ToLower() == subSource.ToLower()))
                .Where(i => !i.IsArchived);
        }

    }
    public class LeadIntegrationDuplicateLeadSpec : Specification<Domain.Entities.Lead>
    {
        public LeadIntegrationDuplicateLeadSpec(List<string> contactNos, LeadSource leadSource, string subSource)
        {
            //TODO : need to change this implementation once custom status is done
            //var Statuses = new List<string>() { "not_interested", "dropped" };
            //var StatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };

            Query.Include(i => i.Enquiries)
                .Where(i => !i.IsDeleted)
                .Where(i => (!string.IsNullOrWhiteSpace(i.ContactNo) && contactNos.Contains(i.ContactNo.Substring(i.ContactNo.Length - 10) ?? string.Empty)))
                //&& (i.Status != null && !Statuses.Contains(i.Status.Status) && !StatusesIds.Contains(i.Status.BaseId)))
                .Where(i => i.Enquiries.Any(i => i.LeadSource == leadSource && !string.IsNullOrWhiteSpace(i.SubSource) && i.SubSource.ToLower() == subSource.ToLower()))
                .Where(i => !i.IsArchived);
        }
    }
    public class BulkLeadByContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public BulkLeadByContactNoSpec(List<string> contactNos) =>
            Query.Where(i => !i.IsDeleted && !i.IsArchived && contactNos.Contains(i.ContactNo));

    }

    public class LeadByContactWithEnquiryNoSpec : Specification<Domain.Entities.Lead>
    {
        public LeadByContactWithEnquiryNoSpec(string contactNo) =>
      Query.Where(i => !i.IsDeleted
                                && !i.IsArchived
                                && (
                                    (i.ContactNo != null && i.ContactNo.Contains(contactNo))
                                    || (i.AlternateContactNo != null && contactNo.Contains(i.AlternateContactNo))
                                   ))
                      .Include(i => i.Enquiries)
                      .Include(i => i.CustomFlags);
    }
    public class GetLeadsByChannelPartnerContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public GetLeadsByChannelPartnerContactNoSpec(GetLeadsByChannelPartnerContactNoRequest request, string contactNo)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.ContactNo != null && i.ChannelPartners.Select(cp => cp.ContactNo).Any(c => c != null && c.Contains(contactNo)));
            Query.OrderByDescending(a => a.LastModifiedOn)
              .Skip(request.PageSize * (request.PageNumber - 1))
              .Take(request.PageSize);
        }
        public GetLeadsByChannelPartnerContactNoSpec(string contactNo)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.ContactNo != null && i.ChannelPartners.Select(cp => cp.ContactNo).Any(c => c != null && c.Contains(contactNo)));
        }
    }
}
