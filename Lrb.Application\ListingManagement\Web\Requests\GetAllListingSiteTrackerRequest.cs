﻿using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Specs;

namespace Lrb.Application.ListingManagement.Web.Requests
{
    public class GetAllListingSiteTrackerRequest : PaginationFilter, IRequest<PagedResponse<ViewListingSiteTrackerDto, string>>
    {
    }

    public class GetAllListingSiteTrackerRequestHandler : IRequestHandler<GetAllListingSiteTrackerRequest, PagedResponse<ViewListingSiteTrackerDto, string>>
    {
        private readonly IRepositoryWithEvents<ListingSiteTracker> _repo;
        public GetAllListingSiteTrackerRequestHandler(IRepositoryWithEvents<ListingSiteTracker> repo)
        {
            _repo = repo;
        }

        public async Task<PagedResponse<ViewListingSiteTrackerDto, string>> Handle(GetAllListingSiteTrackerRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _repo.ListAsync(new GetAllListingSiteTrackerSpecs(request), cancellationToken);
            var count = await _repo.CountAsync(new GetAllListingSiteTrackerCountSpecs());
            var trackerDtos = trackers.Adapt<List<ViewListingSiteTrackerDto>>();
            return new(trackerDtos, count);
        }
    }
}
