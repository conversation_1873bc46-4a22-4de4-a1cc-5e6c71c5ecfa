﻿using Lrb.Application.ListingManagement.Web.Requests;

namespace Lrb.Application.ListingManagement.Web.Specs
{
    public class GetAllListingSiteTrackerSpecs : EntitiesByPaginationFilterSpec<ListingSiteTracker>
    {
        public GetAllListingSiteTrackerSpecs(GetAllListingSiteTrackerRequest filter) : base(filter) 
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        } 
    }

    public class GetAllListingSiteTrackerCountSpecs : Specification<ListingSiteTracker>
    {
        public GetAllListingSiteTrackerCountSpecs()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }

    public class GetListingSiteTrackerByIdSpecs : Specification<ListingSiteTracker>
    {
        public GetListingSiteTrackerByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }
}
