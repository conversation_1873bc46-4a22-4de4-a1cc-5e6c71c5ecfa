﻿using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.Marketing.Web.Specs
{
    public class GetExportCpTrackersSpec : EntitiesByPaginationFilterSpec<ExportMarketingTracker>
    {
        public GetExportCpTrackersSpec(GetAllExportCpTracker filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.ExportType == MarketingExportType.ExportChannelPartner).OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportCpTrackersCountSpec : Specification<ExportMarketingTracker>
    {
        public GetExportCpTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted && i.ExportType==MarketingExportType.ExportChannelPartner);
        }
    }
}
