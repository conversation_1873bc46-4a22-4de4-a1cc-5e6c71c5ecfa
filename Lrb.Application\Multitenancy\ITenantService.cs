﻿using Lrb.Application.Notifications.Dtos;

namespace Lrb.Application.Multitenancy;

public interface ITenantService
{
    Task<List<TenantDto>> GetAllAsync();
    Task<bool> ExistsWithIdAsync(string id);
    Task<bool> ExistsWithNameAsync(string name);
    Task<TenantDto> GetByIdAsync(string id);
    Task<string> CreateAsync(CreateTenantRequest request, CancellationToken cancellationToken);
    Task<string> ActivateAsync(string id);
    Task<string> DeactivateAsync(string id);
    Task<string> UpdateSubscription(string id, DateTime extendedExpiryDate);
    Task<bool> GetAllTenantsFromTenantDBAsync();
    Task<bool> IsTenantAleadyExitsAsync(string id,CancellationToken cancellationToken);
    Task<TenantDto> GetTenantCachebyIdAsync(string tenantId);
    Task<bool> UpdateTenantCacheByIdAsync(string tenantId);
    Task<List<TenantDto>> GetAllTenantsCacheAsync();
    Task<string> SeedingDatabase(TenantDto request, CancellationToken cancellationToken);
}