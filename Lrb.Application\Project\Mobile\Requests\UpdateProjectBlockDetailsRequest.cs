﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Project.Mobile
{
    public class UpdateProjectBlockDetailsRequest : IRequest<Response<Guid>>
    {
        public List<UpdateBlockDto> BlockDtos { get; set; }
        public Guid ProjectId { get; set; }
    }

    public class GetBlocksWithProjectIdSpec : Specification<Block>, ISpecification<Block>
    {
        public GetBlocksWithProjectIdSpec(Guid projectId)
        {
            Query.Where(i => i.ProjectId == projectId);
        }
    }
    public class UpdateProjectBlockDetailsRequestHandler : IRequestHandler<UpdateProjectBlockDetailsRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Block> _blockRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UnitType> _unitTypeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IGooglePlacesService _googlePlacesService;
        public UpdateProjectBlockDetailsRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.Block> blockRepo,
            IRepositoryWithEvents<Domain.Entities.UnitType> unitTypeRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IGooglePlacesService googlePlacesService
            )
        {
            _projectRepo = projectRepo;
            _blockRepo = blockRepo;
            _unitTypeRepo = unitTypeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _addressRepo = addressRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _googlePlacesService = googlePlacesService;
        }
        public async Task<Response<Guid>> Handle(UpdateProjectBlockDetailsRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new ProjectByIdSpec(request.ProjectId))).FirstOrDefault();
            if (project == null)
            {
                throw new NotFoundException("Project does not exist!");
            }
            List<Domain.Entities.Block> blocks = await _blockRepo.ListAsync(new GetBlocksWithProjectIdSpec(project.Id), cancellationToken);
            List<UpdateBlockDto> blockDtos = request.BlockDtos;

            foreach (UpdateBlockDto blockDto in blockDtos)
            {
                var blockToUpdate = blocks.FirstOrDefault(a => a.Id == blockDto.Id);
                if (blockToUpdate != null)
                {
                    await _blockRepo.UpdateAsync(blockDto.Adapt(blockToUpdate));
                }
            }
            return new(project.Id);
        }
    }
}
