﻿using Lrb.Application.Project.Mobile.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Project.Mobile
{
    public class UpdateProjectUnitTypesRequest : IRequest<Response<Guid>>
    {
        public List<UpdateUnitTypeDto> UnitTypes { get; set; }
        public Guid ProjectId { get; set; }
    }
    public class UpdateProjectUnitTypesRequestHandler : IRequestHandler<UpdateProjectUnitTypesRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UnitType> _unitTypeRepo;
        private readonly IRepositoryWithEvents<UnitTypeAttribute> _unitTypeAttributeRepo;
        private readonly IRepositoryWithEvents<CustomMasterAttribute> _masterProjectUnitAttributesRepo;
        public UpdateProjectUnitTypesRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.UnitType> unitTypeRepo,
            IRepositoryWithEvents<UnitTypeAttribute> unitTypeAttributeRepo,
            IRepositoryWithEvents<CustomMasterAttribute> masterProjectUnitAttributesRepo
            )
        {
            _projectRepo = projectRepo;
            _unitTypeRepo = unitTypeRepo;
            _unitTypeAttributeRepo = unitTypeAttributeRepo;
            _masterProjectUnitAttributesRepo = masterProjectUnitAttributesRepo;
        }
        public async Task<Response<Guid>> Handle(UpdateProjectUnitTypesRequest request, CancellationToken cancellationToken)
        {
            if (request?.ProjectId != null)
            {
                Domain.Entities.Project? project = await _projectRepo.GetByIdAsync(request.ProjectId, cancellationToken);
                if (project == null)
                {
                    throw new NotFoundException("Project does not exist!");
                }
            }

            List<UnitType> unitTypes = await _unitTypeRepo.ListAsync(new GetUnitTypebyProjectIdSpec(request.ProjectId), cancellationToken);
            
            if(unitTypes?.Any() ?? false)
            {
                int i = 0;

                foreach (UpdateUnitTypeDto unitTypeDto in request.UnitTypes)
                {
                    UnitType unitType = unitTypeDto.Adapt(unitTypes[i]);
                    unitType.ProjectId = request.ProjectId;
                    unitType.TaxationMode = unitTypeDto.TaxationMode;
                    await _unitTypeRepo.UpdateAsync(unitType);

                    #region Unit Attributes
                    if (unitTypeDto?.UnitAttributes?.Any() ?? false)
                    {
                        foreach (var attribute in unitTypeDto.UnitAttributes)
                        {
                            var fetchUnitTypeAttributes = await _masterProjectUnitAttributesRepo.GetByIdAsync(attribute.MasterProjectUnitAttributeId, cancellationToken);
                            if (fetchUnitTypeAttributes == null)
                            {
                                throw new Exception($"Id does not belong to master property Attribute data.");
                            }
                        }
                        foreach (var unitAttributes in unitTypeDto.UnitAttributes)
                        {
                            var projectAttribute = new UnitTypeAttribute
                            {
                                UnitTypeId = unitType.Id,
                                MasterProjectUnitAttributeId = unitAttributes.MasterProjectUnitAttributeId,
                                Value = unitAttributes.Value
                            };

                            await _unitTypeAttributeRepo.AddAsync(projectAttribute);
                        }
                    }
                    #endregion

                    i++;
                    if (i == request.UnitTypes.Count)
                        break;

                }
            }
            return new(request.ProjectId);
        }
    }


}
