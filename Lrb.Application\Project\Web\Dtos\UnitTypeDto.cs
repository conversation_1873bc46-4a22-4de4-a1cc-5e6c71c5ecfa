﻿using Lrb.Application.Project.Web.Dtos;

namespace Lrb.Application.Project.Web
{

    public class CreateUnitTypeDto : BaseUnitTypeDto
    {
        public Guid ProjectId { get; set; }
        public IEnumerable<UnitTypeAttributeDto>? UnitAttributes { get; set; }
        public Guid? UnitTypeId { get; set; }
        public Dictionary<string, List<UnitInfoGalleryDto>>? ImageUrls { get; set; }
    }
    public class UpdateUnitTypeDto : BaseUnitTypeDto
    {
        public IEnumerable<UnitTypeAttributeDto>? UnitAttributes { get; set; }
        public Guid? UnitTypeId { get; set; }
        public Dictionary<string, List<UnitInfoGalleryDto>>? ImageUrls { get; set; }
    }
    public class ViewUnitTypeDto : BaseUnitTypeDto
    {
        public List<UnitTypeAttributeDto>? Attributes { get; set; }
        public ProjectTypeDto? UnitType {  get; set; }
        public List<UnitInfoGalleryDto>? Images { get; set; }
        public List<UnitInfoGalleryDto>? Videos { get; set; }
        public List<UnitInfoGalleryDto>? UnitInfoGalleries { get; set; }
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
    }
    public class BaseUnitTypeDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public FurnishStatus FurnishingStatus { get; set; }
        public double? NoOfBHK { get; set; }
        public BHKType BHKType { get; set; }
        public Facing Facing { get; set; }
        public double? Area { get; set; }
        public Guid? AreaUnitId { get; set; }
        public double? CarpetArea { get; set; }
        public Guid? CarpetAreaUnitId { get; set; }
        public double? BuildUpArea { get; set; }
        public Guid? BuildUpAreaId { get; set; }
        public double? SuperBuildUpArea { get; set; }
        public Guid? SuperBuildUpAreaUnit { get; set; }
        public double? PricePerUnit { get; set; }
        public double? Price { get; set; }
        public UnitTypeStatus UnitTypeStatus { get; set; }
        public string? Currency { get; set; }
        public long? MaintenanceCost { get; set; }
        public List<Facing>? Facings { get; set; }
        public bool? IsWaterMarkEnabled { get; set; }
        public TaxationMode TaxationMode { get; set; }
    }
}
