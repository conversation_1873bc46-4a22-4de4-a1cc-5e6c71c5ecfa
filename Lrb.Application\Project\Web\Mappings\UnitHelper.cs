﻿using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml;
using Lrb.Domain.Entities.MasterData;
using System.Data;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Linq;
using System.Reflection;
using Lrb.Domain.Entities.AmenitiesAttributes;

namespace Lrb.Application.Project.Web.Mappings
{
    public static class UnitHelper
    {
        public static List<Lrb.Domain.Entities.UnitType> ConvertToUnit(this DataTable dataTable,
            Dictionary<UnitDataColumn, string> dataColumn,
            List<MasterAreaUnit> unitTypes,
            List<MasterProjectType> projectTypes,
            List<CustomMasterAttribute> unitTypesAttributes,
            Lrb.Domain.Entities.GlobalSettings globalSettings,
            Lrb.Domain.Entities.Project project)
        {
            List<UnitType> units = new();
            Guid? defaultUnitId = Guid.TryParse(globalSettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : (Guid?)null;
            foreach (DataRow dataRow in dataTable.Rows)
            {
                string? name = !dataColumn.ContainsKey(UnitDataColumn.Name) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.Name]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.Name]].ToString();
                string? furnishingStatus = !dataColumn.ContainsKey(UnitDataColumn.FurnishingStatus) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.FurnishingStatus]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.FurnishingStatus]].ToString();
                string? bhkType = !dataColumn.ContainsKey(UnitDataColumn.BHKType) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.BHKType]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.BHKType]].ToString();
                string? noOfBhks = !dataColumn.ContainsKey(UnitDataColumn.NoOfBHK) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.NoOfBHK]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.NoOfBHK]].ToString();
                string? facing = !dataColumn.ContainsKey(UnitDataColumn.Facing) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.Facing]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.Facing]].ToString();
                string? unitArea = !dataColumn.ContainsKey(UnitDataColumn.UnitArea) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.UnitArea]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.UnitArea]].ToString();
                string? carpetArea = !dataColumn.ContainsKey(UnitDataColumn.CarpetArea) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.CarpetArea]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.CarpetArea]].ToString();
                string? carpetAreaUnit = !dataColumn.ContainsKey(UnitDataColumn.CarpetAreaUnit) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.CarpetAreaUnit]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.CarpetAreaUnit]].ToString();
                string? builtupArea = !dataColumn.ContainsKey(UnitDataColumn.BuiltupArea) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.BuiltupArea]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.BuiltupArea]].ToString();
                string? builtupAreaUnit = !dataColumn.ContainsKey(UnitDataColumn.BuiltupAreaUnit) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.BuiltupAreaUnit]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.BuiltupAreaUnit]].ToString();
                string? superBuiltupArea = !dataColumn.ContainsKey(UnitDataColumn.SuperBuiltupArea) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.SuperBuiltupArea]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.SuperBuiltupArea]].ToString();
                string? superBuiltupAreaUnit = !dataColumn.ContainsKey(UnitDataColumn.SuperBuiltupAreaUnit) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.SuperBuiltupAreaUnit]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.SuperBuiltupAreaUnit]].ToString();
                string? areaUnit = !dataColumn.ContainsKey(UnitDataColumn.AreaUnit) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.AreaUnit]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.AreaUnit]].ToString();
                string? pricePerUnit = !dataColumn.ContainsKey(UnitDataColumn.PricePerUnit) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.PricePerUnit]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.PricePerUnit]].ToString();
                string? totalPrice = !dataColumn.ContainsKey(UnitDataColumn.TotalPrice) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.TotalPrice]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.TotalPrice]].ToString();
                string? unitType = !dataColumn.ContainsKey(UnitDataColumn.UnitType) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.UnitType]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.UnitType]].ToString();
                string? unitSubType = !dataColumn.ContainsKey(UnitDataColumn.UnitSubType) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.UnitSubType]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.UnitSubType]].ToString();
                string? currecncy = !dataColumn.ContainsKey(UnitDataColumn.Currency) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.Currency]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.Currency]].ToString();
                string? maintenanceCost = !dataColumn.ContainsKey(UnitDataColumn.MaintenanceCost) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.MaintenanceCost]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.MaintenanceCost]].ToString();
                string? balconies = !dataColumn.ContainsKey(UnitDataColumn.Balconies) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.Balconies]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.Balconies]].ToString();
                string? bathRooms = !dataColumn.ContainsKey(UnitDataColumn.BathRooms) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.BathRooms]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.BathRooms]].ToString();
                string? drawingOrLivingRooms = !dataColumn.ContainsKey(UnitDataColumn.DrawingOrLivingRooms) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.DrawingOrLivingRooms]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.DrawingOrLivingRooms]].ToString();
                string? bedRooms = !dataColumn.ContainsKey(UnitDataColumn.BedRooms) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.BedRooms]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.BedRooms]].ToString();
                string? utilities = !dataColumn.ContainsKey(UnitDataColumn.Utilities) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.Utilities]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.Utilities]].ToString();
                string? kitchens = !dataColumn.ContainsKey(UnitDataColumn.Kitchens) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.Kitchens]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.Kitchens]].ToString();
                string? maximumOccupants = !dataColumn.ContainsKey(UnitDataColumn.MaximumOccupants) || string.IsNullOrEmpty(dataColumn[UnitDataColumn.MaximumOccupants]) ? string.Empty : dataRow[dataColumn[UnitDataColumn.MaximumOccupants]].ToString();

                var projectInfo = GetProjectType(unitType, unitSubType, projectTypes, bhkType, noOfBhks);
                string? currencycode;
                if (!string.IsNullOrEmpty(currecncy))
                {
                    currencycode = dataColumn.GetCurrencySymbol1(dataRow, currecncy, globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency);
                }
                else
                {
                    currencycode = project?.MonetaryInfo?.Currency;
                }
                var unitarea = GetUnitDetails(unitArea ?? string.Empty, unitTypes, areaUnit ?? string.Empty, defaultUnitId);
                var carpetarea = GetUnitDetails(carpetArea ?? string.Empty, unitTypes, carpetAreaUnit ?? string.Empty, unitarea.Item2 );
                var builtuparea = GetUnitDetails(builtupArea ?? string.Empty, unitTypes, builtupAreaUnit ?? string.Empty, unitarea.Item2);
                var superbuiltuparea = GetUnitDetails(superBuiltupArea ?? string.Empty, unitTypes, superBuiltupAreaUnit ?? string.Empty, unitarea.Item2);

                List<UnitTypeAttribute>? newAttributes=null;
                if(balconies==string.Empty && bathRooms == string.Empty && drawingOrLivingRooms == string.Empty && bedRooms == string.Empty && utilities == string.Empty && kitchens == string.Empty && maximumOccupants == string.Empty)
                {
                    newAttributes = null;
                }
                else
                {
                    newAttributes = GetUnitTypeAttribute(new Dictionary<UnitDataColumn, string>()
                    {
                        { UnitDataColumn.Balconies, balconies ?? string.Empty},
                        { UnitDataColumn.BathRooms, bathRooms ?? string.Empty},
                        { UnitDataColumn.DrawingOrLivingRooms, drawingOrLivingRooms ?? string.Empty},
                        { UnitDataColumn.BedRooms,bedRooms ?? string.Empty},
                        { UnitDataColumn.Utilities,utilities ?? string.Empty},
                        { UnitDataColumn.Kitchens,kitchens ?? string.Empty},
                        { UnitDataColumn.MaximumOccupants,maximumOccupants ?? string.Empty}
                    }, unitTypesAttributes);
                }
                var unit = new UnitType()
                {
                    Name = name,
                    Area = unitarea.Item1,
                    CarpetArea = carpetarea.Item1 != 0 ? carpetarea.Item1 : unitarea.Item1,
                    CarpetAreaUnitId = carpetarea.Item2,
                    BuildUpArea = builtuparea.Item1 != 0 ? builtuparea.Item1 : unitarea.Item1,
                    BuildUpAreaId = builtuparea.Item2,
                    SuperBuildUpArea = superbuiltuparea.Item1 != 0 ? superbuiltuparea.Item1 : unitarea.Item1,
                    SuperBuildUpAreaUnit = superbuiltuparea.Item2,
                    PricePerUnit = ConvertStringToDouble(pricePerUnit),
                    Price = ConvertStringToDouble(totalPrice),
                    FurnishingStatus = ConvertStringToEnum<FurnishStatus>(furnishingStatus),
                    Facings = GetFacings(facing),
                    AreaUnitId = unitarea.Item2,
                    BHKType = projectInfo.IsValidInfo ? projectInfo.BHKType : default,
                    NoOfBHK = projectInfo.IsValidInfo ? projectInfo.NoOfBHK : default,
                    MasterUnitType = projectInfo.IsValidInfo ? projectInfo.ProjectType : default,
                    Currency = currencycode ?? project?.MonetaryInfo?.Currency ?? "INR",
                    MaintenanceCost = ConvertStringToLong(maintenanceCost),
                    Attributes = newAttributes
                };
                units.Add(unit);
            }
            return units;
        }
        public static List<Facing> GetFacings(string facings)
        {
            List<Facing> facing = new();
            if (!string.IsNullOrEmpty(facings))
            {
                foreach (string item in facings.Split(','))
                {
                    string cleanedItem = RemoveSpecialCharactersAndNumbers(item);

                    if (Enum.TryParse<Facing>(cleanedItem, true, out Facing type))
                    {
                        facing.Add(type);
                    }
                }
            }
            return facing;
        }

        private static string RemoveSpecialCharactersAndNumbers(string input)
        {
            return Regex.Replace(input, @"[^a-zA-Z]", "").ToLower();
        }
        public static double ConvertStringToDouble(string? input)
        {
            double result;
            if (double.TryParse(input, out result))
            {
                return result;
            }
            else
            {
                return default;
            }
        }
        public static long ConvertStringToLong(string? input)
        {
            long result;
            if (long.TryParse(input, out result))
            {
                return result;
            }
            else
            {
                return default;
            }
        }
        public static TEnum ConvertStringToEnum<TEnum>(string? value) where TEnum : struct
        {
            string status = RemoveSpecialCharactersAndNumbers(value);
            if (status.Equals("fullyfurnished"))
            {
                status = "furnished";
            }
            if (Enum.TryParse(status, true, out TEnum result))
            {
                return result;
            }
            else
            {
                return default;
            }
        }
        public static Guid? GetUnitId(string? input, List<MasterAreaUnit> unitTypes)
        {
            if (string.IsNullOrEmpty(input))
            {
                return null;
            }
            else
            {
                if (unitTypes?.Any(i => i.Unit.Trim().ToLower().Contains(input?.ToLower()?.Trim() ?? string.Empty)) ?? false)
                {
                    return unitTypes.FirstOrDefault(i => i.Unit.Trim().ToLower().Contains(input?.ToLower()?.Trim() ?? string.Empty))?.Id ?? Guid.Empty;
                }
            }
            return Guid.Empty;
        }

        public static List<string> GetUnmappedUnitColumnNames(this DataTable table, Dictionary<UnitDataColumn, string> mappedColumnsData)
        {
            List<string> columns = new();
            foreach (DataColumn column in table.Columns)
            {
                if (!mappedColumnsData.ContainsValue(column.ColumnName) && !column.ColumnName.Contains("S. No"))
                {
                    if (!columns.Contains(column.ColumnName))
                    {
                        columns.Add(column.ColumnName);
                    }
                }
            }
            return columns;
        }
        public static void SetUnit(this Domain.Entities.UnitType unit, Guid userId)
        {
            if (unit != null)
            {
                unit.CreatedBy = userId;
                unit.LastModifiedBy = userId;
            }

        }
        public static MemoryStream CreateExcelData(List<InvalidUnitDto> inValidData)
        {
            using MemoryStream stream = new MemoryStream();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();

            workbookpart.Workbook = new DocumentFormat.OpenXml.Spreadsheet.Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild<Sheets>(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "mySheet"
            };
            sheets.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;

            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            List<string> headers = new();
            var obj = inValidData.FirstOrDefault();
            Type objType = obj.GetType();

            Row row1 = new Row();
            foreach (var item in headers)
            {
                Cell cell1 = new Cell()
                {
                    CellValue = new CellValue(item),
                    DataType = CellValues.String,

                };
                row1.Append(cell1);
            }

            sheetData.Append(row1);
            foreach (var inValid in inValidData)
            {
                Row row = new Row();
                CreateCell(row, inValid.Name);
                CreateCell(row, inValid.Area);
                CreateCell(row, inValid.Notes);
                CreateCell(row, inValid.Errors);
                CreateCell(row, inValid.RepeatedCount.ToString(), CellValues.Number);
                sheetData.Append(row);

            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument.Close();
            return stream;
        }
        public static void CreateCell(Row row, string value, CellValues type = CellValues.String)
        {
            Cell cell = new Cell()
            {
                CellValue = new CellValue(value),
                DataType = type,
            };
            row.Append(cell);
        }
        public static ProjectTypeInfo GetProjectType(string baseProjectType, string subProjectType, List<MasterProjectType> projectTypes, string bhkType, string? noOfBHK)
        {
            MasterProjectType projectType = null;
            List<string> projecTypes = projectTypes?.Select(i => i.DisplayName.ToLower()).ToList();
            BHKType bHKType = default;
            double bHK = GetNoOfBHK(noOfBHK);
            if (!string.IsNullOrEmpty(bhkType) || !string.IsNullOrEmpty(noOfBHK))
            {
                //if ((Enum.TryParse<BHKType>(bhkType, true, out bHKType) || bHK != 0) && !projecTypes.Contains(subProjectType.ToLower()))
                //{
                //    return new ProjectTypeInfo()
                //    {
                //        InvalidBHKType = bhkType,
                //        InvalidNoOfBHK = noOfBHK,
                //        IsValidInfo = false,
                //        BaseProjectType = baseProjectType,
                //        SubProjectType = subProjectType
                //    };
                //}
                if ((Enum.TryParse<BHKType>(bhkType, true, out bHKType) || bHK != 0) && (string.IsNullOrEmpty(baseProjectType) && string.IsNullOrEmpty(subProjectType)))
                {
                    projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.Id == Guid.Parse("8ba96762-16f2-4735-bc5a-138573081a19"));
                    return new ProjectTypeInfo()
                    {
                        ProjectType = projectType,
                        BHKType = bHKType,
                        NoOfBHK = bHK,
                        IsValidInfo = true
                    };
                }
            }
            else
            {
                if (string.IsNullOrEmpty(baseProjectType) && string.IsNullOrEmpty(subProjectType))
                {
                    return new ProjectTypeInfo() { IsValidInfo = false };
                }
            }
            string subProject = string.Empty;
            string baseProject = string.Empty;
            if (!string.IsNullOrEmpty(subProjectType))
            {
                subProject = subProjectType;
            }
            else
            {
                if (!string.IsNullOrEmpty(baseProjectType))
                {
                    if (baseProjectType.ToLower().Contains("residential"))
                    {
                        subProject = "flat";
                    }
                    if (baseProjectType.ToLower().Contains("commercial"))
                    {
                        subProject = "Plot";
                    }
                    if (baseProjectType.ToLower().Contains("agricultural"))
                    {
                        subProject = "land";
                    }
                }
            }

            if (!string.IsNullOrEmpty(baseProjectType) && baseProjectType?.ToLower() == "commercial")
            {
                projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower().Trim() == subProject.ToLower().Trim() && !i.IsDeleted);   
            }
            if (string.IsNullOrEmpty(baseProjectType) || baseProjectType?.ToLower() == "residential")
            {
                projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower().Trim() == subProject.ToLower().Trim() && !i.IsDeleted);
            }
            else
            {
                projectType = projectTypes.FirstOrDefault(i => i.DisplayName.ToLower() == subProject.ToLower());
            }
            return new ProjectTypeInfo()
            {
                ProjectType = projectType,
                BHKType = bHKType,
                NoOfBHK = bHK,
                IsValidInfo = true
            };
        }
        public static string GetCurrencySymbol1(this Dictionary<UnitDataColumn, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return defaultcurrency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            var currencies = ISO3166.Country.List
           .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
           .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
           .SelectMany(c => c.Currencies)
           .Select(currency => currency.IsoCode))
           .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);

            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }

        }

        public static bool TryGetCurrencySymbol(string currencySymbol, out string isoCurrencyCode)
        {
            isoCurrencyCode = CultureInfo
        .GetCultures(CultureTypes.AllCultures)
        .Where(c => !c.IsNeutralCulture)
        .Select(culture =>
        {
            try
            {
                return new RegionInfo(culture.Name);
            }
            catch
            {
                return null;
            }
        })
        .Where(ri => ri != null && ri.CurrencySymbol.Equals(currencySymbol, StringComparison.OrdinalIgnoreCase))
        .Select(ri => ri.ISOCurrencySymbol)
        .FirstOrDefault();

            return isoCurrencyCode != null;
        }
        public static List<string> GetValidCurrencySymbols()
        {
            var symbols = new List<string>();
            var countries = ISO3166.Country.List;

            var currencyProvider = new Nager.Country.CountryProvider();

            foreach (var country in countries)
            {
                var matchingCountry = currencyProvider.GetCountries()
                    .FirstOrDefault(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase));

                if (matchingCountry != null)
                {
                    foreach (var currency in matchingCountry.Currencies)
                    {
                        var symbol = currency.IsoCode;
                        symbols.Add(symbol);
                    }
                }
            }

            return symbols;

        }
        public static double GetArea(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?$");
                Match match = regex.Match(number);
                double res = 0;

                if (match.Success)
                {
                    res = double.Parse(match.Value);
                }
                return res;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        public static (double, Guid) GetUnitDetails(string unitAreaSize, List<MasterAreaUnit> areaUnits, string unit, Guid? areaUnitId = null)
        {
            var unitArea = GetArea(unitAreaSize);
            if (areaUnits.Count > 0)
            {
                var masterUnits = areaUnits?.ConvertAll(i => i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)?.ToList();
                var unitOfArea = Regex.Replace(unitAreaSize, "[a-zA-Z]", string.Empty).Trim();
                if (!string.IsNullOrWhiteSpace(unitOfArea) && !string.IsNullOrWhiteSpace(unit) && (masterUnits?.Any(i => i.Contains(unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)) ?? false))
                {
                    var normalizedUnit = unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty;
                    var unitId = areaUnits?.FirstOrDefault(i => (i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty).Contains(normalizedUnit))?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
                else if (areaUnitId != null)
                {
                    return (unitArea, areaUnitId ?? Guid.Empty);
                }
                else if (string.IsNullOrWhiteSpace(unit))
                {
                    var unitId = areaUnits?.FirstOrDefault(i => i.Unit == "Sq. Feet")?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                } 
            }
            return (unitArea, Guid.Empty);
        }
        public static double GetNoOfBHK(string noOfBHK)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?");
                Match match = regex.Match(noOfBHK);
                double integer = 0;

                if (match.Success)
                {
                    integer = double.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        public static List<UnitTypeAttribute> GetUnitTypeAttribute(Dictionary<UnitDataColumn, string> attributes, List<CustomMasterAttribute> masterProjectUnitAttributes)
        {
            List<UnitTypeAttribute> unitTypeAttributes = new List<UnitTypeAttribute>();
            foreach (var attribute in attributes)
            {
                var attributeValue = GetNumber(attribute.Value);
                UnitTypeAttribute unitTypeAttribute = new();
                if (masterProjectUnitAttributes.Count > 0)
                {
                    var formattedKey = SplitCamelCase(attribute.Key.ToString()).ToLower();
                    var matchingAttribute = masterProjectUnitAttributes.FirstOrDefault(i => Regex.Replace(i?.AttributeDisplayName?.ToLower() ?? string.Empty, "[^a-zA-Z]+", " ").Contains(formattedKey));
                    if (matchingAttribute != null)
                    {
                        unitTypeAttribute.MasterProjectUnitAttributeId = masterProjectUnitAttributes.FirstOrDefault(i => Regex.Replace(i?.AttributeDisplayName?.ToLower() ?? string.Empty, "[^a-zA-Z]+", " ").Contains(formattedKey))?.Id ?? Guid.Empty;
                        unitTypeAttribute.Value = attributeValue.ToString();
                        unitTypeAttributes.Add(unitTypeAttribute);
                    }
                }
            }
            return unitTypeAttributes;
        }
        public static double GetNumber(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+");
                Match match = regex.Match(number);
                double integer = 0;

                if (match.Success)
                {
                    integer = int.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        private static string SplitCamelCase(string input)
        {
            return Regex.Replace(input, "(?<=[a-z])([A-Z])", " $1", RegexOptions.Compiled).Trim();
        }

        public static List<InvalidUnitDto> GetInvalidUnits(List<Domain.Entities.UnitType> units)
        {
            List<InvalidUnitDto> invalidData = new();
        
            List<Domain.Entities.UnitType>? invalidunits = null;
            invalidunits = units.Where(i => string.IsNullOrEmpty(i.Name)).ToList();
            if (invalidunits.Any())
            {
                var invalidName = invalidunits.Adapt<List<InvalidUnitDto>>();
                invalidName.ForEach(i => i.Errors = "Invalid Name");
                units.RemoveAll(i => string.IsNullOrEmpty(i.Name));
                invalidData.AddRange(invalidName);
                invalidunits = null;
            }


            invalidunits = units.Where(i => i.Area == default).ToList();
            if (invalidunits.Any())
            {
                var invalidArea = invalidunits.Adapt<List<InvalidUnitDto>>();
                invalidArea.ForEach(i => i.Errors = "Invalid Area");
                units.RemoveAll(i => i.Area == default);
                invalidData.AddRange(invalidArea);
                invalidunits = null;
            }

            invalidunits = units.Where(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.BHKType!=0).ToList();
            if (invalidunits.Any())
            {
                var invalidBhkType = invalidunits.Adapt<List<InvalidUnitDto>>();
                invalidBhkType.ForEach(i => i.Errors = "Invalid BhkType");
                units.RemoveAll(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id==Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.BHKType != 0);
                invalidData.AddRange(invalidBhkType);
                invalidunits = null;
            }


            invalidunits = units.Where(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.NoOfBHK != 0).ToList();
            if (invalidunits.Any())
            {
                var invalidbhk = invalidunits.Adapt<List<InvalidUnitDto>>();
                invalidbhk.ForEach(i => i.Errors = "Invalid NoofBhk");
                units.RemoveAll(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.NoOfBHK != 0);
                invalidData.AddRange(invalidbhk);
                invalidunits = null;
            }

            invalidunits = units.Where(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.Attributes!=null ).ToList();
            if (invalidunits.Any())
            {
                var invalidAttributes = new List<InvalidUnitDto>();
                foreach (var unit in invalidunits)
                {
                    string attributes = string.Empty;
                    if (unit.Attributes != null)
                    {
                        attributes = string.Join(", ", unit.Attributes.Select(a => a.Value));
                    }
                    var invalidUnitDto = unit.Adapt<InvalidUnitDto>();
                    invalidUnitDto.Attributes = attributes;
                    invalidUnitDto.Errors = "Invalid Attributes";
                    invalidAttributes.Add(invalidUnitDto);
                }
                units.RemoveAll(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.Attributes != null);
                invalidData.AddRange(invalidAttributes);
                invalidunits = null;
            }
            return invalidData;
        }
    }
}
