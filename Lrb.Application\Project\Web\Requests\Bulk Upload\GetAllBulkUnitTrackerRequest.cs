﻿using Lrb.Application.DataManagement.Web.Request.Bulk_Upload;
using Lrb.Application.Project.Web.Specs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Requests.Bulk_Upload
{
    public class GetAllBulkUnitTrackerRequest : PaginationFilter, IRequest<PagedResponse<BulkUnitUploadTracker, string>>
    {
    }
    public class GetAllBulkUnitrackerRequestHandler : IRequestHandler<GetAllBulkUnitTrackerRequest, PagedResponse<BulkUnitUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkUnitUploadTracker> _bulkUnitTracker;
        public GetAllBulkUnitrackerRequestHandler(IRepositoryWithEvents<BulkUnitUploadTracker> bulkUnitTracker)
        {
            _bulkUnitTracker = bulkUnitTracker;
        }
        public async Task<PagedResponse<BulkUnitUploadTracker, string>> Handle(GetAllBulkUnitTrackerRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _bulkUnitTracker.ListAsync(new BulkUnitUploadSpec(request), cancellationToken);
            var totalCount = await _bulkUnitTracker.CountAsync(cancellationToken);
            return new(trackers, totalCount);
        }

    }
}
