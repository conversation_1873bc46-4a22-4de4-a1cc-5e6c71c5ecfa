﻿namespace Lrb.Application.Project.Web.Requests.Bulk_Upload
{
    public class GetProjectBulkUploadTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkProjectUploadTracker, string>>
    {
    }
    public class GetProjectBulkUploadTrackersRequestHandler : IRequestHandler<GetProjectBulkUploadTrackersRequest, PagedResponse<BulkProjectUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkProjectUploadTracker> _trackerRepo;
        public GetProjectBulkUploadTrackersRequestHandler(IRepositoryWithEvents<BulkProjectUploadTracker> trackerRepo)
        {
            _trackerRepo = trackerRepo;
        }
        public async Task<PagedResponse<BulkProjectUploadTracker, string>> Handle(GetProjectBulkUploadTrackersRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _trackerRepo.ListAsync(new GetAllBulkProjectTrackerSpec(request), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new GetBulkProjectTrackerCountSpec(), cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
