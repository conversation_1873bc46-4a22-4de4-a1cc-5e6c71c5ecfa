﻿using Lrb.Application.Project.Web.Specs;

namespace Lrb.Application.Project.Web.Requests
{
    public class BulkDeleteProjectRequest : IRequest<Response<bool>>
    {
        public List<Guid>? ProjectIds { get; set; }
    }

    public class BulkDeleteProjectRequestHandler : IRequestHandler<BulkDeleteProjectRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly ICurrentUser _currentUser;
        public BulkDeleteProjectRequestHandler(IRepositoryWithEvents<Domain.Entities.Project> projectRepo, ICurrentUser currentUser)
        {
            _projectRepo = projectRepo;
            _currentUser = currentUser;
        }

        public async Task<Response<bool>> Handle(BulkDeleteProjectRequest request, CancellationToken cancellationToken)
        {
            if(request.ProjectIds == null)
            {
                throw new InvalidOperationException("Provide Ids to delete");
            }
            var projects = await _projectRepo.ListAsync(new GetProjectByIdsSpecs(request.ProjectIds), cancellationToken);
            if(projects?.Any() ?? false)
            {
                foreach (var project in projects)
                {
                    project.IsArchived = true;
                    project.ArchivedBy = _currentUser.GetUserId();
                    project.ArchivedOn = DateTime.UtcNow;
                    await _projectRepo.UpdateAsync(project);
                }
                return new(true);
            }
            return new(false, "Project not found by those Ids");
            
            
        }
    }
}
