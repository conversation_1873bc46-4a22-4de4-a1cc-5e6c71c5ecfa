﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using static Lrb.Application.ZonewiseLocation.Web.Specs.LocationCountSpec;

namespace Lrb.Application.Project.Web
{
    public class CreateUnitTypeRequest : CreateUnitTypeDto, IRequest<Response<Guid>>
    {
    }
    public class CreateUnitTypeRequestHandler : IRequestHandler<CreateUnitTypeRequest, Response<Guid>>
    {
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Block> _blockRepo;
        private readonly IRepositoryWithEvents<UnitType> _unitTypeRepo;
        private readonly IGooglePlacesService _googlePlacesService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<UnitTypeAttribute> _unitTypeAttributeRepo;
        private readonly IRepositoryWithEvents<CustomMasterAttribute> _masterProjectUnitAttributesRepo;
        private readonly IRepositoryWithEvents<CustomMasterProjectType> _customProjectTypeRepo;
        private readonly IRepositoryWithEvents<MasterProjectType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<UnitInfoGallery> _unitGalleryRepo;
        public CreateUnitTypeRequestHandler(
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterProjectType> masterProjecttypeRepo,
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Block> blockRepo,
            IRepositoryWithEvents<UnitType> unitTypeRepo,
            IGooglePlacesService googlePlacesService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<UnitTypeAttribute> unitTypeAttributeRepo,
            IRepositoryWithEvents<CustomMasterAttribute> masterProjectUnitAttributesRepo,
            IRepositoryWithEvents<CustomMasterProjectType> customProjectTypeRepo,
            IRepositoryWithEvents<UnitInfoGallery> unitGalleryRepo)
        {
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _addressRepo = addressRepo;
            _projectRepo = projectRepo;
            _blockRepo = blockRepo;
            _unitTypeRepo = unitTypeRepo;
            _googlePlacesService = googlePlacesService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _unitTypeAttributeRepo = unitTypeAttributeRepo;
            _masterProjectUnitAttributesRepo = masterProjectUnitAttributesRepo;
            _customProjectTypeRepo = customProjectTypeRepo;
            _propertyTypeRepo = masterProjecttypeRepo;
            _unitGalleryRepo = unitGalleryRepo;
        }

        public async Task<Response<Guid>> Handle(CreateUnitTypeRequest request, CancellationToken cancellationToken)
        {
            var project = await _projectRepo.GetByIdAsync(request.ProjectId, cancellationToken);
            if (project == null)
            {
                throw new NotFoundException("No Project found by the id.");
            }
            MasterProjectType? masterProjectType = null;
            if (request.UnitTypeId != Guid.Empty && request.UnitTypeId != default)
            {
                masterProjectType = await _propertyTypeRepo.GetByIdAsync(request.UnitTypeId ?? Guid.Empty, cancellationToken);
                if (masterProjectType == null)
                {
                    throw new InvalidOperationException("UnitTypes type id does not belong to Master data.");
                }
            }
            UnitType unitType = request.Adapt<UnitType>();
            unitType.ProjectId = request.ProjectId;
            unitType.MasterUnitType = masterProjectType;
            unitType.TaxationMode = request.TaxationMode;
            try
            {
                unitType = await _unitTypeRepo.AddAsync(unitType);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateUnitTypeRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

            #region Unit Attributes
            if (request?.UnitAttributes?.Any() ?? false)
            {
                foreach (var attribute in request.UnitAttributes)
                {
                    var fetchUnitTypeAttributes = await _masterProjectUnitAttributesRepo.FirstOrDefaultAsync(new GetAttributeByIdsSpec(attribute.MasterProjectUnitAttributeId));
                    if (fetchUnitTypeAttributes == null)
                    {
                        throw new Exception($"Id does not belong to master Project Unit Attribute data.");
                    }
                }
                foreach (var unitAttributes in request.UnitAttributes)
                {
                    var projectAttribute = new UnitTypeAttribute
                    {
                        UnitTypeId = unitType.Id,
                        MasterProjectUnitAttributeId = unitAttributes.MasterProjectUnitAttributeId,
                        Value = unitAttributes.Value
                    };

                    await _unitTypeAttributeRepo.AddAsync(projectAttribute);
                }
            }
            #endregion

            #region Unit Gallery

            if(request?.ImageUrls?.Any() ?? false)
            {
                foreach (var keyValuePair in request.ImageUrls)
                {
                    foreach (var imagePath in keyValuePair.Value)
                    {
                        if (!string.IsNullOrWhiteSpace(imagePath.ImageFilePath) && !string.IsNullOrWhiteSpace(keyValuePair.Key))
                        {
                            UnitInfoGallery unitGallery = new()
                            {
                                ImageKey = keyValuePair.Key,
                                ImageFilePath = imagePath.ImageFilePath,
                                UnitId = unitType.Id,
                                IsCoverImage = imagePath.IsCoverImage,
                                Name = imagePath.Name,
                                GalleryType = imagePath?.GalleryType ?? ProjectGalleryType.None,
                            };
                            await _unitGalleryRepo.AddAsync(unitGallery, cancellationToken);
                        }
                    }
                }
            }

            #endregion

            return new(unitType.Id);
        }
    }
}
