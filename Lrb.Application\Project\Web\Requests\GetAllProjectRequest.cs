﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Project.Web.Requests.CommonHandler;
using Lrb.Application.Reports.Web;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Graph;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetAllProjectParameter : PaginationFilter
    {
        public ProjectVisibilityType ProjectVisibility { get; set; }
        public List<ProjectStatus>? ProjectStatuses { get; set; }
        public ProjectCurrentStatus? CurrentStatus { get; set; }
        public ProjectType? ProjectType { get; set; }
        public Facing? Facing { get; set; }
        public List<string>? ProjectName { get; set; }
        public List<string>? BuilderName { get; set; }
        public List<Guid>? ProjectSubType { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Search { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public PossesionType? Possesion { get; set; }
        public List<string>? Locations { get; set; }
        public double? CarpetArea { get; set; }
        public Guid? CarpetAreaUnitId { get; set; }
        public List<Guid>? AmenitesIds { get; set; }
        public string? Currency { get; set; }
        public List<Facing>? Facings { get; set; }
        public int? MinLeadCount { get; set; } 
        public int? MaxLeadCount { get; set; } 
        public int? MaxProspectCount { get; set; } 
        public int? MinProspectCount { get; set; } 
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public long? FromMinPrice { get; set; }
        public long? ToMinPrice { get; set; }
        public long? FromMaxPrice { get; set; }
        public long? ToMaxPrice { get; set; }
    }
    public class GetAllProjectRequest : GetAllProjectParameter, IRequest<PagedResponse<ViewProjectDto, string>>
    {
    }
    public class GetAllProjectRequestHandler : ProjectCommonRequestHandler, IRequestHandler<GetAllProjectRequest, PagedResponse<ViewProjectDto, string>>
    {
        private readonly IProjectRepository _efProjectRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllProjectRequestHandler(
            IServiceProvider serviceProvider,
            IProjectRepository efProjectRepository,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser) : base(serviceProvider)
        {
            _efProjectRepository = efProjectRepository;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ViewProjectDto, string>> Handle(GetAllProjectRequest request, CancellationToken cancellationToken)
        {
            List<Guid> projectIds = new();
            if (request.MinLeadCount != null || request.MaxLeadCount != null || request.MinProspectCount != null || request.MaxProspectCount != null)
            {
                var tenantId = _currentUser.GetTenant();
                var project = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ProjectIdsDto>("LeadratBlack", "Lead&Prospects_ProjectAssociatedCountFilter", new
                {
                    tenantid = tenantId,
                    minprospectcount = request.MinProspectCount,
                    maxprospectcount = request.MaxProspectCount,
                    minleadcount = request.MinLeadCount,
                    maxleadcount = request.MaxLeadCount
                })).FirstOrDefault()?.ProjectIds ?? new List<Guid>();
                projectIds = project.ToList();
            }
            var projects = await _efProjectRepository.GetAllProjectsForWebNewAsync(request, projectIds);
            var count = await _efProjectRepository.GetAllProjectsCountForWebNewAsync(request, projectIds);
            List<ViewProjectDto> viewProjects = projects.Adapt<List<ViewProjectDto>>();
            return new PagedResponse<ViewProjectDto, string>(viewProjects, count);
        }
    }

    public enum ProjectVisibilityType
    {
        All,
        Residential,
        Commercial,
        Agriculture,
        Deleted
    }

    public enum ProjectType
    {
        All,
        Residential,
        Commercial,
        Agricultural,
    }
    public class ProjectIdsDto
    {
        public IList<Guid>? ProjectIds { get; set; }
    }
}
