﻿using Lrb.Application.Project.Web.Specs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetProjectBasicDetailsByIdRequest :  IRequest<Response<ViewProjectDto>>
    {
        public Guid Id { get; set; }
        public GetProjectBasicDetailsByIdRequest(Guid id)
        {
            Id = id;
        }
    }

    public class GetProjectBasicDetailsByIdRequestHandler : IRequestHandler<GetProjectBasicDetailsByIdRequest, Response<ViewProjectDto>>
    {
        public readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        public GetProjectBasicDetailsByIdRequestHandler(IRepositoryWithEvents<Domain.Entities.Project> projectRepo)
        {
            _projectRepo = projectRepo;
        }
        public async Task<Response<ViewProjectDto>> Handle(GetProjectBasicDetailsByIdRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new GetProjectBasicDetailsByIdSpec(request.Id), cancellationToken)).FirstOrDefault();
            if (project == null) { throw new NotFoundException("No Project found by this Id"); }
            var projectDto = project.Adapt<ViewProjectDto>();
            return new Response<ViewProjectDto>(projectDto);

        }
    }
}
