﻿using Lrb.Application.Project.Web.Requests.CommonHandler;
using Lrb.Application.Project.Web.Specs;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetProjectBlocksRequest : PaginationFilter, IRequest<PagedResponse<ViewBlockDto, string>>
    {
        public Guid ProjectId { get; set; }
    }

    public class GetProjectBlocksRequestHandler : ProjectCommonRequestHandler, IRequestHandler<GetProjectBlocksRequest, PagedResponse<ViewBlockDto, string>>
    {
        public GetProjectBlocksRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {

        }
        public async Task<PagedResponse<ViewBlockDto, string>> Handle(GetProjectBlocksRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new GetProjectByIdForUnitAndBlockSpecs(request.ProjectId))).FirstOrDefault();
            if (project == null)
            {
                throw new NotFoundException("Project Not Found By Id");
            }
            var blocks = await _blockRepo.ListAsync(new GetAllProjectBlocksSpecs(request), cancellationToken);
            var count = await _blockRepo.CountAsync(new GetAllProjectBlocksSpecs(request), cancellationToken);
            var blockDtos = blocks.Adapt<List<ViewBlockDto>>();
            return new(blockDtos, count);
        }
    }
}
