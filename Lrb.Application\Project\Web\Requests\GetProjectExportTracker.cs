﻿using Lrb.Application.ExportTemplate;
using Lrb.Application.Identity.Users;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Specs;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetProjectExportTracker : PaginationFilter, IRequest<PagedResponse<ExportProjectTrackerDto, string>>
    {
    }
    public class GetProjectExportTrackerHandler : IRequestHandler<GetProjectExportTracker, PagedResponse<ExportProjectTrackerDto, string>>
    {
        private readonly IReadRepository<ExportProjectTracker> _exportProjectTrackerRepo;
        public readonly IUserService _userService;
        public GetProjectExportTrackerHandler(IReadRepository<ExportProjectTracker> exportProjectTrackerRepo,
            IUserService userService)
        {
            _exportProjectTrackerRepo = exportProjectTrackerRepo;
            _userService = userService;
        }
        public async Task<PagedResponse<ExportProjectTrackerDto, string>> Handle(GetProjectExportTracker request, CancellationToken cancellationToken)
        {
            var leadTrackers = await _exportProjectTrackerRepo.ListAsync(new ExportProjectTrackerSpec(request), cancellationToken);
            var totalCount = await _exportProjectTrackerRepo.CountAsync(new GetExportProjectTrackersCountSpec(), cancellationToken);
            var projectTrackerDto = leadTrackers.Adapt<List<ExportProjectTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(projectTrackerDto.Select(i => i.CreatedBy.ToString()).ToList(), cancellationToken);
            foreach (var projectTracker in projectTrackerDto)
            {
                projectTracker.ExportTemplate = (JsonConvert.DeserializeObject<ViewProjectExportTemplateDto>(projectTracker?.Template ?? string.Empty) ?? null);
                projectTracker.ExportedUser = users?.FirstOrDefault(i => projectTracker.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }
            return new(projectTrackerDto, totalCount);
        }
    }
}
