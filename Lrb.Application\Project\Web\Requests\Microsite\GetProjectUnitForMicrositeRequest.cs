﻿using Lrb.Application.Project.Web.Dtos.Microsite;
using Lrb.Application.Project.Web.Specs;

namespace Lrb.Application.Project.Web.Requests.Microsite
{
    public class GetProjectUnitForMicrositeRequest : IRequest<Response<ViewMicrositeUnitAndBlock>>
    {
        public string SerialNo { get; set; }
        public GetProjectUnitForMicrositeRequest(string serialNo) => SerialNo = serialNo;
    }

    public class GetProjectUnitForMicrositeRequestHandler : IRequestHandler<GetProjectUnitForMicrositeRequest, Response<ViewMicrositeUnitAndBlock>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        public GetProjectUnitForMicrositeRequestHandler(IRepositoryWithEvents<Domain.Entities.Project> projectRepo)
        {
            _projectRepo = projectRepo;
        }

        public async Task<Response<ViewMicrositeUnitAndBlock>> Handle(GetProjectUnitForMicrositeRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new GetProjectUnitForMicrositeSpecs(request.SerialNo), cancellationToken)).FirstOrDefault();
            if (project == null)
            {
                throw new NotFoundException("Project not found by this serial no");
            }
            var unitAndBlockInfos = project.Adapt<ViewMicrositeUnitAndBlock>();
            return new(unitAndBlockInfos);
        }
    }
}
