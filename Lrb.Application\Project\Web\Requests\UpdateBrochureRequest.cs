﻿using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;

namespace Lrb.Application.Project.Web.Requests
{
    public class UpdateBrochureRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public List<BrochureDto> BrochureDtos { get; set; } = new List<BrochureDto>();
        public List<string>? Links { get; set; }

    }

    public class UpdateBrochureRequestHandler : IRequestHandler<UpdateBrochureRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public UpdateBrochureRequestHandler(IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _projectRepo = projectRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<Response<bool>> Handle(UpdateBrochureRequest request, CancellationToken cancellationToken)
        {
            if (request.Id == Guid.Empty || request.Id == default)
            {
                throw new InvalidDataException("Invalid Project Id.");
            }
            var project = (await _projectRepo.ListAsync(new ProjectByIdSpec(request.Id), cancellationToken)).FirstOrDefault();
            try
            {
                if (project != null)
                {
                    if (request.BrochureDtos?.Any() ?? false)
                    {
                        project.Brochures = request.BrochureDtos.Adapt<List<Brochure>>();
                    }
                    else
                    {
                        project.Brochures = null;
                    }
                    project.Links = request.Links;
                    await _projectRepo.UpdateAsync(project);
                    return new(true);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdateBrochureRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return new(false);
        }
    }
}
