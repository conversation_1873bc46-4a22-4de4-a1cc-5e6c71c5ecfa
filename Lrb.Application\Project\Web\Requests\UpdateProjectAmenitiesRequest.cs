﻿using Lrb.Application.Project.Web.Specs;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using static Lrb.Application.ZonewiseLocation.Web.Specs.LocationCountSpec;

namespace Lrb.Application.Project.Web.Requests
{
    public class UpdateProjectAmenitiesRequest : IRequest<Response<bool>>
    {
        public Guid ProjectId { get; set; }
        public IEnumerable<Guid>? Amenities { get; set; }
    }

    public class UpdateProjectAmenitiesRequestHandler : IRequestHandler<UpdateProjectAmenitiesRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<ProjectAmenity> _projectAmenityRepo;
        private readonly IRepositoryWithEvents<CustomMasterAmenity> _masterProjectAmenityRepo;

        public UpdateProjectAmenitiesRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<ProjectAmenity> projectAmenityRepo,
            IRepositoryWithEvents<CustomMasterAmenity> masterProjectAmenityRepo)
        {
            _projectRepo = projectRepo;
            _projectAmenityRepo = projectAmenityRepo;
            _masterProjectAmenityRepo = masterProjectAmenityRepo;
        }
        public async Task<Response<bool>> Handle(UpdateProjectAmenitiesRequest request, CancellationToken cancellationToken)
        {
            var project = await _projectRepo.GetByIdAsync(request.ProjectId);
            if (project == null)
            {
                throw new NotFoundException("No project found by the id.");
            }
            var amenities = await _projectAmenityRepo.ListAsync(new GetProjectAmenitiesByProjectIdSpecs(project.Id), cancellationToken);
            await _projectAmenityRepo.DeleteRangeAsync(amenities, cancellationToken);
            if (request?.Amenities?.Any() ?? false)
            {
                foreach (var amenityId in request.Amenities)
                {
                    var fetchedPropertyAmenity = await _masterProjectAmenityRepo.FirstOrDefaultAsync(new GetAmenityByIdsSpec(amenityId));
                    if (fetchedPropertyAmenity != null)
                    {
                        var projectAmenity = new ProjectAmenity();
                        projectAmenity.MasterProjectAmenityId = amenityId;
                        projectAmenity.ProjectId = project.Id;
                        await _projectAmenityRepo.AddAsync(projectAmenity);
                    }
                }
            }
            return new(true);
        }
    }
}
