﻿using Lrb.Application.Project.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;
using static Lrb.Application.ZonewiseLocation.Web.Specs.LocationCountSpec;

namespace Lrb.Application.Project.Web.Requests
{
    public class UpdateProjectUnitTypesRequest : IRequest<Response<Guid>>
    {
        public List<UpdateUnitTypeDto> UnitTypes { get; set; }
        public Guid ProjectId { get; set; }
    }
    public class UpdateProjectUnitTypesRequestHandler : IRequestHandler<UpdateProjectUnitTypesRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UnitType> _unitTypeRepo;
        private readonly IRepositoryWithEvents<UnitTypeAttribute> _unitTypeAttributeRepo;
        private readonly IRepositoryWithEvents<CustomMasterAttribute> _masterProjectUnitAttributesRepo;
        private readonly IRepositoryWithEvents<CustomMasterProjectType> _customProjectTypeRepo;
        private readonly IRepositoryWithEvents<MasterProjectType> _masterProjectTypeRepo;
        private readonly IRepositoryWithEvents<UnitInfoGallery> _unitGalleryRepo;
        public UpdateProjectUnitTypesRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.UnitType> unitTypeRepo,
            IRepositoryWithEvents<UnitTypeAttribute> unitTypeAttributeRepo,
            IRepositoryWithEvents<CustomMasterAttribute> masterProjectUnitAttributesRepo,
            IRepositoryWithEvents<CustomMasterProjectType> customProjectTypeRepo,
            IRepositoryWithEvents<MasterProjectType> masterProjectTypeRepo,
            IRepositoryWithEvents<UnitInfoGallery> unitGalleryRepo
            )
        {
            _projectRepo = projectRepo;
            _unitTypeRepo = unitTypeRepo;
            _unitTypeAttributeRepo = unitTypeAttributeRepo;
            _masterProjectUnitAttributesRepo = masterProjectUnitAttributesRepo;
            _customProjectTypeRepo = customProjectTypeRepo;
            _masterProjectTypeRepo = masterProjectTypeRepo;
            _unitGalleryRepo = unitGalleryRepo;
        }
        public async Task<Response<Guid>> Handle(UpdateProjectUnitTypesRequest request, CancellationToken cancellationToken)
        {
            if (request?.ProjectId != null)
            {
                Domain.Entities.Project? project = await _projectRepo.GetByIdAsync(request.ProjectId, cancellationToken);
                if (project == null)
                {
                    throw new NotFoundException("Project does not exist!");
                }
            }

            List<UnitType> unitTypes = await _unitTypeRepo.ListAsync(new GetUnitTypebyProjectIdSpec(request.ProjectId), cancellationToken);
            if (unitTypes?.Any() ?? false)
            {
                foreach (UpdateUnitTypeDto unitTypeDto in request.UnitTypes)
                {
                    var existingUnitType = (unitTypes.Where(i => i.Id == unitTypeDto.Id)).FirstOrDefault();
                    if(existingUnitType != null)
                    {
                        MasterProjectType? masterProjectType = null;
                        if (unitTypeDto.UnitTypeId != Guid.Empty && unitTypeDto.UnitTypeId != default)
                        {
                            masterProjectType = await _masterProjectTypeRepo.GetByIdAsync(unitTypeDto.UnitTypeId ?? Guid.Empty, cancellationToken);
                            if (masterProjectType == null)
                            {
                                throw new InvalidOperationException("UnitTypes type id does not belong to Master data.");
                            }
                        }
                        UnitType unitType = unitTypeDto.Adapt(existingUnitType);
                        unitType.ProjectId = request.ProjectId;
                        unitType.MasterUnitType = masterProjectType;
                        unitType.TaxationMode = unitTypeDto.TaxationMode;
                        await _unitTypeRepo.UpdateAsync(unitType);

                        #region Unit Attributes
                        var attributes = await _unitTypeAttributeRepo.ListAsync(new GetAllUnitAttributesByUnitIdSpecs(unitType.Id), cancellationToken);
                        if (unitTypeDto?.UnitAttributes?.Any() ?? false)
                        {
                            foreach (var attribute in unitTypeDto.UnitAttributes)
                            {
                                var fetchUnitTypeAttributes = await _masterProjectUnitAttributesRepo.FirstOrDefaultAsync(new GetAttributeByIdsSpec(attribute.MasterProjectUnitAttributeId));
                                if (fetchUnitTypeAttributes == null)
                                {
                                    throw new Exception($"Id does not belong to master property Attribute data.");
                                }
                            }
                            await _unitTypeAttributeRepo.DeleteRangeAsync(attributes);
                            foreach (var unitAttributes in unitTypeDto.UnitAttributes)
                            {
                                var projectAttribute = new UnitTypeAttribute
                                {
                                    UnitTypeId = unitType.Id,
                                    MasterProjectUnitAttributeId = unitAttributes.MasterProjectUnitAttributeId,
                                    Value = unitAttributes.Value
                                };

                                await _unitTypeAttributeRepo.AddAsync(projectAttribute);
                            }
                        }
                        #endregion

                        #region Unit Gallery
                        if(unitTypeDto?.ImageUrls?.Any() ?? false)
                        {
                            var unitGalleries = await _unitGalleryRepo.ListAsync(new GalleryByUnitIdspecs(unitType.Id), cancellationToken);
                            foreach (var unitGallery in unitGalleries)
                            {
                                await _unitGalleryRepo.SoftDeleteAsync(unitGallery, cancellationToken);
                            }
                            foreach (var keyValuePair in unitTypeDto.ImageUrls)
                            {
                                foreach (var imagePath in keyValuePair.Value)
                                {
                                    if (!string.IsNullOrWhiteSpace(imagePath.ImageFilePath) && !string.IsNullOrWhiteSpace(keyValuePair.Key))
                                    {
                                        UnitInfoGallery unitGallery = new()
                                        {
                                            ImageKey = keyValuePair.Key,
                                            ImageFilePath = imagePath.ImageFilePath,
                                            UnitId = unitType.Id,
                                            IsCoverImage = imagePath.IsCoverImage,
                                            Name = imagePath.Name,
                                            GalleryType = imagePath?.GalleryType ?? ProjectGalleryType.None,
                                        };
                                        await _unitGalleryRepo.AddAsync(unitGallery, cancellationToken);
                                    }
                                }
                            }
                        }
                        #endregion
                    }
                }
            }
            return new(request.ProjectId);
        }
    }
}
