﻿using Lrb.Application.Project.Web.Requests.Bulk_Upload;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Specs
{
    public class BulkUnitUploadSpec : EntitiesByPaginationFilterSpec<BulkUnitUploadTracker>
    {
        public BulkUnitUploadSpec(GetAllBulkUnitTrackerRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }

}
