﻿using Lrb.Application.Project.Web.Requests;
using Lrb.Application.Property.Web.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Specs
{
    public class ExportProjectTrackerSpec : EntitiesByPaginationFilterSpec<ExportProjectTracker>
    {
        public ExportProjectTrackerSpec(GetProjectExportTracker filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted).OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetExportProjectTrackersCountSpec : Specification<ExportProjectTracker>
    {
        public GetExportProjectTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}
