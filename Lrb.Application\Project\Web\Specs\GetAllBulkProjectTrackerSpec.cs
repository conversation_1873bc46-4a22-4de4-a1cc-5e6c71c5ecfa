﻿using Lrb.Application.Project.Web.Requests.Bulk_Upload;

namespace Lrb.Application.Project.Web
{
    public class GetAllBulkProjectTrackerSpec : EntitiesByPaginationFilterSpec<BulkProjectUploadTracker>
    {
        public GetAllBulkProjectTrackerSpec(GetProjectBulkUploadTrackersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetBulkProjectTrackerCountSpec : Specification<BulkProjectUploadTracker>
    {
        public GetBulkProjectTrackerCountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}
