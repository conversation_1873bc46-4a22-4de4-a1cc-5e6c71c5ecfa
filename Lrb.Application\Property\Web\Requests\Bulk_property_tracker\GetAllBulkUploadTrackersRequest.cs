﻿using Lrb.Application.Property.Web.Specs;

namespace Lrb.Application.Property.Web
{
    public class GetAllBulkUploadTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkPropertyUploadTracker, string>>
    {
    }
    public class GetAllBulkUploadTrackersRequestHandler : IRequestHandler<GetAllBulkUploadTrackersRequest, PagedResponse<BulkPropertyUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkPropertyUploadTracker> _trackerRepo;
        public GetAllBulkUploadTrackersRequestHandler(IRepositoryWithEvents<BulkPropertyUploadTracker> trackerRepo)
        {
            _trackerRepo = trackerRepo;
        }
        public async Task<PagedResponse<BulkPropertyUploadTracker, string>> Handle(GetAllBulkUploadTrackersRequest request, CancellationToken cancellationToken)
        {
            var trackers = await _trackerRepo.ListAsync(new GetAllBulkPropertyTrackerSpec(request), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new GetBulkPropertyTrackerCountSpec(), cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
