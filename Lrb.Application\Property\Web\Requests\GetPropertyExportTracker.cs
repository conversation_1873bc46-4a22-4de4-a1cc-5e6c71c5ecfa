﻿using Lrb.Application.ExportTemplate;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead;
using Lrb.Application.Property.Web.Dtos;
using Microsoft.Graph;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Application.Property.Web.Specs;



namespace Lrb.Application.Property.Web.Requests
{
    public class GetPropertyExportTracker : PaginationFilter, IRequest<PagedResponse<ExportPropertyTrackerDto, string>>
    {



    }
    public class GetPropertyExportTrackerhandler : IRequestHandler<GetPropertyExportTracker, PagedResponse<ExportPropertyTrackerDto, string>>
    {
        private readonly IReadRepository<ExportPropertyTracker> _exportPropertyTrackerRepo;
        public readonly IUserService _userService;
        public GetPropertyExportTrackerhandler(IReadRepository<ExportPropertyTracker> exportLeadTrackerRepo,
            IUserService userService)
        {
            _exportPropertyTrackerRepo = exportLeadTrackerRepo;
            _userService = userService;
        }
        public async Task<PagedResponse<ExportPropertyTrackerDto, string>> Handle(GetPropertyExportTracker request, CancellationToken cancellationToken)
        {
            var leadTrackers = await _exportPropertyTrackerRepo.ListAsync(new ExportPropertyTrackerSpec(request), cancellationToken);
            var totalCount = await _exportPropertyTrackerRepo.CountAsync(new GetExportPropertyTrackersCountSpec(), cancellationToken);
            var leadTrackerDto = leadTrackers.Adapt<List<ExportPropertyTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(leadTrackerDto.Select(i => i.CreatedBy.ToString()).ToList(), cancellationToken);
            foreach (var leadTracker in leadTrackerDto)
            {
                leadTracker.ExportTemplate = (JsonConvert.DeserializeObject<ViewExportTemplateDto>(leadTracker?.Template ?? string.Empty) ?? null);
                leadTracker.ExportedUser = users?.FirstOrDefault(i => leadTracker.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }
            return new(leadTrackerDto, totalCount);
        }
    }
}


