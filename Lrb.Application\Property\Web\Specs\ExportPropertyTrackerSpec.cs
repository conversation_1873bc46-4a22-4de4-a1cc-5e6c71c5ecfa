﻿using Lrb.Application.Property.Web.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;



namespace Lrb.Application.Property.Web.Specs
{
    public class ExportPropertyTrackerSpec : EntitiesByPaginationFilterSpec<ExportPropertyTracker>
    {
        public ExportPropertyTrackerSpec(GetPropertyExportTracker filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted).OrderByDescending(i => i.LastModifiedOn);
        }
    }



    public class GetExportPropertyTrackersCountSpec : Specification<ExportPropertyTracker>
    {
        public GetExportPropertyTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}