﻿namespace Lrb.Application.Property.Web.Specs
{
    public class GetAllBulkPropertyTrackerSpec : EntitiesByPaginationFilterSpec<BulkPropertyUploadTracker>
    {
        public GetAllBulkPropertyTrackerSpec(GetAllBulkUploadTrackersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetBulkPropertyTrackerCountSpec : Specification<BulkPropertyUploadTracker>
    {
        public GetBulkPropertyTrackerCountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}
