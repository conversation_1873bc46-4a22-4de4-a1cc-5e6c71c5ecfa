﻿namespace Lrb.Application.Reports.Web
{
    public class UserActivityReportLevel10Dto : IDto
    {
        public Guid UserId { get; set; }
        public string? UserName { get; set; }
        public int? CallsInitiatedCount { get; set; }
        public int? CallsInitiatedLeadsCount { get; set; }
        public int? WhatsAppInitiatedCount { get; set; }
        public int? WhatsAppInitiatedLeadsCount { get; set; }
        public int? EmailsInitiatedCount { get; set; }
        public int? EmailsInitiatedLeadsCount { get; set; }
        public int? SMSInitiatedCount { get; set; }
        public int? SMSInitiatedLeadsCount { get; set; }
    }
}
