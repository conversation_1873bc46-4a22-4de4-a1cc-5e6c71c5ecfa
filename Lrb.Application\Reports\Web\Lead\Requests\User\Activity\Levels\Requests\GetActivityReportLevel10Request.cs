﻿using Lrb.Application.Utils;

namespace Lrb.Application.Reports.Web
{
    public class GetActivityReportLevel10Request : IRequest<PagedResponse<UserActivityReportLevel10Dto, string>>
    {
        public string? SearchText { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public UserStatus? UserStatus { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
    }
    public class GetActivityReportLevel10RequestHandler : IRequestHandler<GetActivityReportLevel10Request, PagedResponse<UserActivityReportLevel10Dto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetActivityReportLevel10RequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<UserActivityReportLevel10Dto, string>> Handle(GetActivityReportLevel10Request request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.UtcNow.Date.ConvertFromDateToUtc();
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.UtcNow.Date.ConvertToDateToUtc();
            var dataFromHistory = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportLevel10Dto>("LeadratBlack", "Lead_GetActivityReportFromLeadCommunicationsLevel3", new
            {
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                from_date = request.FromDate,
                to_date = request.ToDate,
                tenant_id = tenantId,
                user_ids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower().Trim(),
                userstatus = request?.UserStatus ?? 0
            }, 300)).ToList();
            return new()
            {
                Items = dataFromHistory,
                Succeeded = true,
                TotalCount = 0,
            };
        }
    }
}
