﻿using Lrb.Application.Utils;

namespace Lrb.Application.Reports.Web
{
    public class GetActivityReportLevel11Request : IRequest<PagedResponse<UserActivityReportLevel11Dto, string>>
    {
        public string? SearchText { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public UserStatus? UserStatus { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
    }
    public class GetActivityReportLevel11RequestHandler : IRequestHandler<GetActivityReportLevel11Request, PagedResponse<UserActivityReportLevel11Dto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetActivityReportLevel11RequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<UserActivityReportLevel11Dto, string>> Handle(GetActivityReportLevel11Request request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.UtcNow.Date.ConvertFromDateToUtc();
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.UtcNow.Date.ConvertToDateToUtc();
            var dataFromLeadAppointment = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportLevel11Dto>("LeadratBlack", "Lead_GetActivityReportFromLeadAppointmentLevel4", new
            {
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                user_ids = teamUserIds,
                tenant_id = tenantId,
                from_date = request.FromDate,
                to_date = request.ToDate
            }, 300)).ToList();
            var logDtos = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<AttendanceLogReportDto>("LeadratBlack", "Lead_GetActivityReportFromAttendanceLevel4", new
            {
                user_ids = dataFromLeadAppointment.Select(i => i.UserId)?.ToList() ?? new(),
                tenant_id = tenantId,
                from_date = request.FromDate,
                to_date = request.ToDate,
            }, 300)).ToList();
            dataFromLeadAppointment.ForEach(i =>
            {
                var logsForCurrentUser = logDtos.Where(log => log.UserId == i.UserId).OrderByDescending(l => l.CreatedOn).ToList();
                var logsByDay = GetLogsByDateRange(logsForCurrentUser, request.FromDate.Value, request.ToDate.Value);
                i.AverageWorkingHours = GetWorkingHoursByUser(logsByDay);
                i.MeetingDoneCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.MeetingDoneCount ?? 0;
                i.MeetingDoneUniqueCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.MeetingDoneUniqueCount ?? 0;
                i.SiteVisitDoneUniqueCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.SiteVisitDoneUniqueCount ?? 0;
                i.SiteVisitDoneCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.SiteVisitDoneCount ?? 0;
            });
            return new()
            {
                Items = dataFromLeadAppointment,
                Succeeded = true,
                TotalCount = 0,
            };
        }
        private static TimeSpan GetWorkingHoursByUser(List<LogsByDayReportDto> logsByDayDtos)
        {
            var timeSpanList = logsByDayDtos.Where(i => i != null && i.WorkingHours != null).Select(i => i.WorkingHours).ToList();
            TimeSpan totalHours = new();

            int noOfDays = 0;
            timeSpanList.ForEach(i =>
            {
                if (i != null)
                {
                    totalHours += i.Value;
                    noOfDays++;
                }
            });
            return totalHours / noOfDays;
        }
        private static List<LogsByDayReportDto> GetLogsByDateRange(List<AttendanceLogReportDto> userEntries, DateTime fromDate, DateTime toDate)
        {
            List<LogsByDayReportDto> logsByDay = new();
            while (fromDate <= toDate)
            {
                var startDate = fromDate;
                var endDate = fromDate.AddDays(1).AddSeconds(-1);
                var entriesPerDay = userEntries.Where(i => (i.ClockInTime >= startDate && i.ClockInTime <= endDate)
                                                               || (i.ClockOutTime >= startDate && i.ClockOutTime <= endDate));
                logsByDay.Add(new LogsByDayReportDto()
                {
                    Day = endDate.Date,
                    logDtos = entriesPerDay.ToList(),
                    WorkingHours = entriesPerDay.Any(i => i.ClockOutTime == null) ? TimeSpan.Zero : GetWorkingHours(entriesPerDay.ToList()),
                    AreSwipesMissing = entriesPerDay.Any(i => i.ClockOutTime == null) ? true : false
                });
                fromDate = fromDate.AddDays(1);
            }
            return logsByDay;
        }
        private static TimeSpan GetWorkingHours(List<AttendanceLogReportDto> logDtos)
        {
            TimeSpan hours = new();
            logDtos.ForEach(i =>
            {
                if (i.ClockInTime != null && i.ClockOutTime != null)
                {
                    var time = (i.ClockOutTime.Value) - (i.ClockInTime.Value);
                    hours = hours + time;
                }
            });
            return hours;
        }
    }
}
