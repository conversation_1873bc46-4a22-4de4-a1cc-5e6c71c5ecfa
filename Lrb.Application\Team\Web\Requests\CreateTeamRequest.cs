﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Team.Web.Dtos;

namespace Lrb.Application.Team.Web
{
    public class CreateTeamRequest : BaseTeamLeadRotationInfoDto, IRequest<Response<Guid>>
    {
        public int NoOfReassignment { get; set; }
    }
    public class CreateTeamRequestHandler : IRequestHandler<CreateTeamRequest, Response<Guid>>
    {
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.Team> _teamRepository;
        private readonly IRepositoryWithEvents<UserTeam> _userTeamRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.CustomMasterLeadStatus> _statusRepo;
        public CreateTeamRequestHandler(
            IRepositoryWithEvents<UserTeam> userTeamRepository,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.Team> teamRepository,
            IRepositoryWithEvents<Domain.Entities.MasterData.CustomMasterLeadStatus> statusRepo,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _userTeamRepository = userTeamRepository;
            _userService = userService;
            _teamRepository = teamRepository;
            _userTeamRepository = userTeamRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _statusRepo = statusRepo;
        }

        public async Task<Response<Guid>> Handle(CreateTeamRequest request, CancellationToken cancellationToken)
        {
            var team = await _teamRepository.FirstOrDefaultAsync(new GetTeamByNameSpecs(request.Name ?? string.Empty), cancellationToken);
            if (team != null)
            {
                throw new ConflictException("Team Already Exists!");
            }
            if (request.Configuration != null)
            {
                List<TeamConfiguration> configurations = new List<TeamConfiguration>();
                var configuration = request.Configuration.Adapt<TeamConfiguration>();
                if ((configuration?.IntegrationAccountIds?.Any() ?? false) && (request?.IntegrationAccountInfoId != null && request.IntegrationAccountInfoId != Guid.Empty))
                {
                    configuration.IntegrationAccountIds.Add(request.IntegrationAccountInfoId ?? Guid.Empty);
                }
                else if (configuration != null && (request?.IntegrationAccountInfoId != null && request.IntegrationAccountInfoId != Guid.Empty))
                {
                    configuration.IntegrationAccountIds = new List<Guid>() { request.IntegrationAccountInfoId ?? Guid.Empty };
                }
                configurations.Add(configuration);
                Lrb.Domain.Entities.Team newTeam = request.Adapt<Domain.Entities.Team>();
                newTeam.Configurations = configurations;
                await _teamRepository.AddAsync(newTeam);
                return new(newTeam.Id);
            }
            else
            {
                Lrb.Domain.Entities.Team newTeam = request.Adapt<Domain.Entities.Team>();
                await _teamRepository.AddAsync(newTeam);
                return new(newTeam.Id);
            }
        }
    }
}
