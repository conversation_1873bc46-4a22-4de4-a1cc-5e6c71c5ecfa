﻿using Lrb.Application.Automation.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Microsoft.Graph;
using Microsoft.Graph.TermStore;
using Newtonsoft.Json;

namespace Lrb.Application.Team.Web
{
    public class DeleteTeamRequest : IRequest<Response<bool>>
    {
        public List<Guid> Ids { get; set; }
        public DeleteTeamRequest(List<Guid> ids) => Ids = ids;
    }
    public class DeleteTeamRequestHandler : IRequestHandler<DeleteTeamRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Team> _teamRepository;
        private readonly IRepositoryWithEvents<UserTeam> _userTeamRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        public DeleteTeamRequestHandler(IRepositoryWithEvents<Domain.Entities.Team> teamRepository,
            IRepositoryWithEvents<UserTeam> userTeamRepository, ILeadRepositoryAsync leadRepositoryAsync, IRepositoryWithEvents<UserAssignment> userAssignmentRepo)
        {
            _userTeamRepository = userTeamRepository;
            _teamRepository = teamRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _userAssignmentRepo = userAssignmentRepo;
        }

        public async Task<Response<bool>> Handle(DeleteTeamRequest request, CancellationToken cancellationToken)
        {
            List<Domain.Entities.Team> teams = null;
            try
            {
                teams = await _teamRepository.ListAsync(new TeamByIdSpec(request.Ids), cancellationToken);
                if (teams == null) { throw new NotFoundException("No team found by this Id!"); };
                foreach (var existingTeam in teams)
                {
                    if ((existingTeam.UserIds?.Any() ?? false))
                        await SyncUserAssignmentsAsync(existingTeam, cancellationToken);
                }
                await _teamRepository.DeleteRangeAsync(teams, cancellationToken);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "DeleteTeamRequestHandler ->Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return new(true);
        }
        private async Task SyncUserAssignmentsAsync(Domain.Entities.Team team, CancellationToken cancellationToken)
        {
            var assignments = await _userAssignmentRepo.ListAsync(new GetUserAssignmentByEntityIdSpec(team.Id), cancellationToken);
            var teamUserIds = team.UserIds?.ToHashSet() ?? new();

            foreach (var assignment in assignments)
            {
                if (assignment?.UserAssignmentType == UserAssignmentType.Team &&
                    (team.UserIds?.Count() ?? 0) <= (assignment?.UserIds?.Count() ?? 0))
                {
                    assignment.UserIds = null;
                    await _userAssignmentRepo.UpdateAsync(assignment);
                }
            }
        }
    }
}