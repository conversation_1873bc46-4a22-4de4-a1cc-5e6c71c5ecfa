﻿using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.Team.Web.Requests
{
    public class RemoveTeamUserRequest : IRequest<Response<bool>>
    {
        public Guid? TeamId { get; set; }
        public List<Guid>? UserIds { get; set; }
    }

    public class RemoveTeamUserRequestHandler : IRequestHandler<RemoveTeamUserRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Team> _teamRepository;
        private readonly IRepositoryWithEvents<UserTeam> _userTeamRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public RemoveTeamUserRequestHandler(IRepositoryWithEvents<Domain.Entities.Team> teamRepository,
            IRepositoryWithEvents<UserTeam> userTeamRepository, ILeadRepositoryAsync leadRepositoryAsync)
        {
            _userTeamRepository = userTeamRepository;
            _teamRepository = teamRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<Response<bool>> Handle(RemoveTeamUserRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request?.UserIds?.Any() ?? false)
                {
                    var team = await _teamRepository.FirstOrDefaultAsync(new TeamByIdSpec(request.TeamId ?? Guid.Empty), cancellationToken) ?? throw new NotFoundException("No team found by this Id!");
                    team.UserIds = team?.UserIds?.Where(i => !request.UserIds.Contains(i)).ToList();
                    if (team?.UserIds?.Count() <= 1)
                    {
                        team.IsRotationEnabled = false;
                    }
                    await _teamRepository.UpdateAsync(team);
                    return new(true);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "RemoveTeamUserRequestHandler ->Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return new(false);
        }
    }
}
