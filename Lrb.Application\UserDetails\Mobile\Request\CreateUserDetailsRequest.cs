﻿using Lrb.Application.OrgProfile.Mobile;

namespace Lrb.Application.UserDetails.Mobile
{
    public class CreateUserDetailsRequest : IRequest<Response<Guid>>
    {
        public Guid UserId { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? Address { get; set; }
        public string? Email { get; set; }
        public string? AltEmail { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? PermanentAddress { get; set; }
        public string? PhoneNumber { get; set; }
        public string? EmpNo { get; set; }
        public Guid? ReportsTo { get; set; }
        public Guid? DepartmentId { get; set; }
        public Guid? DesignationId { get; set; }
        public string? Description { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }

    }
    public class CreateUserRequestHandler : IRequestHandler<CreateUserDetailsRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepository;
        private readonly IRepositoryWithEvents<Department> _departmentRepository;
        private readonly IRepositoryWithEvents<Designation> _designationsRepository;
        private readonly IRepositoryWithEvents<UserDocument> _userDocumentRepository;
        private readonly IRepositoryWithEvents<Domain.Entities.Profile> _profileRepo;
        public CreateUserRequestHandler(IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepository,
             IRepositoryWithEvents<Department> departmentRepository,
             IRepositoryWithEvents<Designation> designationRepository,
             IRepositoryWithEvents<UserDocument> userDocumentRepository,
             IRepositoryWithEvents<Domain.Entities.Profile> repoProfile
            )
        {
            _userDetailsRepository = userDetailsRepository;
            _departmentRepository = departmentRepository;
            _designationsRepository = designationRepository;
            _userDocumentRepository = userDocumentRepository;
            _profileRepo = repoProfile;
        }
        public async Task<Response<Guid>> Handle(CreateUserDetailsRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.UserDetails user = new Domain.Entities.UserDetails();
            user = request.Adapt<Domain.Entities.UserDetails>();
            user.CurrentAddress = request.Address;
            user.IsAutomationEnabled = true;
            #region JobDetails
            if (request.ReportsTo == null || request.ReportsTo == Guid.Empty)
            {
                user.ReportsTo = Guid.Empty;
            }
            else
            {
                user.ReportsTo = request.ReportsTo.Value;
            }
            Department? department = null;
            department = await _departmentRepository.GetByIdAsync(request.DepartmentId);
            if (department != null) { user.Department = department; }
            Designation? designation = null;
            designation = await _designationsRepository.GetByIdAsync(request.DesignationId);
            if (designation != null) { user.Designation = designation; }
            if (request.OfficeName != null || request.OfficeName != null)
            {
                var orgProfile = (await _profileRepo.ListAsync(new GetProfileSpec(), cancellationToken)).FirstOrDefault();
                if (orgProfile != null)
                {
                    user.OfficeAddress = orgProfile?.Address?.ToString();
                    user.OfficeName = orgProfile?.DisplayName;
                }
            }
            #endregion
            await _userDetailsRepository.AddAsync(user, cancellationToken);
            return new(user.Id);
        }
    }
}

