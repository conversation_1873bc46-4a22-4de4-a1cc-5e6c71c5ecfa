﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile;

namespace Lrb.Application.UserDetails.Mobile
{
    public class DeleteUserRequest : IRequest<Response<bool>>
    {
        public Guid UserId { get; set; }

        public DeleteUserRequest(Guid userId)
        {
            UserId = userId;
        }
    }
    public class DeleteUserRequestHandler : IRequestHandler<DeleteUserRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userRepository;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> _integartionAccRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.FacebookAccountsInfo> _faceBookRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public DeleteUserRequestHandler(IRepositoryWithEvents<Domain.Entities.UserDetails> userRepository, IUserService userService,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integartionAccRepo,
            IRepositoryWithEvents<FacebookAccountsInfo> faceBookRepo,
            IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _userRepository = userRepository;
            _userService = userService;
            _leadRepo = leadRepo;
            _prospectRepo = prospectRepo;
            _projectRepo = projectRepo;
            _propertyRepo = propertyRepo;
            _integartionAccRepo = integartionAccRepo;
            _faceBookRepo = faceBookRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<Response<bool>> Handle(DeleteUserRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var user = (await _userRepository.ListAsync(new GetUserDetailsByIdSpec(request.UserId), cancellationToken)).FirstOrDefault();
            if (user == null) { throw new NotFoundException("No user found."); }
            var isAdmin = await _dapperRepository.IsAdminAsync(request.UserId, tenantId ?? string.Empty);
            if(isAdmin)
            {
                throw new ConflictException("Administrators Profile's cannot be delete");
            }
            var leadsCount = (await _leadRepo.CountAsync(new GetAssignedLeadsByUserIdSpec(user.UserId), cancellationToken));
            var prospectCount = (await _prospectRepo.CountAsync(new GetAssignedProspectByUserIdSpec(user.UserId), cancellationToken));
            var projectCount = (await _projectRepo.CountAsync(new GetAssignedProjectByUserIdSpec(user.UserId), cancellationToken));
            var propertyCount = (await _propertyRepo.CountAsync(new GetAssignedPropertyByUserIdSpec(user.UserId), cancellationToken));
            var integrationAccountInfo = (await _integartionAccRepo.CountAsync(new GetAssignedIntegrationByUserIdSpec(user.UserId), cancellationToken));
            var facebookAccountInfo = (await _faceBookRepo.CountAsync(new GetAssignedFacebookIntegrationByUserIdSpec(user.UserId), cancellationToken));
            if (leadsCount > 0 || prospectCount > 0 || projectCount > 0 || propertyCount > 0 || integrationAccountInfo > 0 || facebookAccountInfo > 0)
            {
                throw new InvalidOperationException($"User can't be deleted! User has assigned {leadsCount} - Leads , {prospectCount} - prospects,{projectCount} - Projects, {propertyCount} - Properties, {integrationAccountInfo} - Integration accounts, or {facebookAccountInfo} - Facebook accounts. Reassign them.");
            }
            await _userRepository.SoftDeleteAsync(user, cancellationToken);
            await _userService.DeleteAsync(request.UserId.ToString(), cancellationToken);
            return new(true);
        }
    }
}
