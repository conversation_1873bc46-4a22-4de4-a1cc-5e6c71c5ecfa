﻿using Lrb.Application.Identity.Users;

namespace Lrb.Application.UserDetails.Mobile
{
    public class UpdateUserImgRequest : IRequest<Response<Guid>>
    {
        public string? ImageUrl { get; set; }

        public UpdateUserImgRequest(string? imageUrl)
        {
            ImageUrl = imageUrl;
        }
    }
    public class UpdateUserImgRequestHandler : IRequestHandler<UpdateUserImgRequest, Response<Guid>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        public UpdateUserImgRequestHandler(ICurrentUser currentUser, IUserService userService)
        {
            _currentUser = currentUser;
            _userService = userService;
        }

        public async Task<Response<Guid>> Handle(UpdateUserImgRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var user = await _userService.GetAsync(userId.ToString(), cancellationToken);
            if (user == null) { throw new NotFoundException("No user found."); }
            if (request.ImageUrl != null)
            {
                await _userService.UpdateImageAsync(user.Id.ToString(), request.ImageUrl);
            }
            return new(user.Id);
        }
    }
}
