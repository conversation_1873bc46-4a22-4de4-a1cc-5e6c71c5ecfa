﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.UserDetails.Web
{
    public class DeleteUserRequest : IRequest<Response<UserDataCountDto>>
    {
        public Guid UserId { get; set; }

        public DeleteUserRequest(Guid userId)
        {
            UserId = userId;
        }
    }
    public class DeleteUserRequestHandler : IRequestHandler<DeleteUserRequest, Response<UserDataCountDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userRepository;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.FacebookAdsInfo> _facebookRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.IVRAssignment> _ivrAssignment;
        private readonly IRepositoryWithEvents<Domain.Entities.User.DeletedUser> _deleteUsers;
        private readonly IRepositoryWithEvents<Domain.Entities.City> _cityAssignments;
        private readonly IRepositoryWithEvents<Domain.Entities.Location> _localityAssignments;
        private readonly IRepositoryWithEvents<Domain.Entities.Zone> _zoneAssignments;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public DeleteUserRequestHandler(
            IRepositoryWithEvents<Domain.Entities.UserDetails> userRepository,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<Domain.Entities.FacebookAdsInfo> facebookRepo,
            IRepositoryWithEvents<Domain.Entities.IVRAssignment> ivrAssignment,
            IRepositoryWithEvents<Domain.Entities.User.DeletedUser> deleteUsers,
            IRepositoryWithEvents<Domain.Entities.City> cityAssignments,
            IRepositoryWithEvents<Domain.Entities.Location> localityAssignments,
            IRepositoryWithEvents<Domain.Entities.Zone> zoneAssignments,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser
)
        {
            _userRepository = userRepository;
            _userService = userService;
            _leadRepo = leadRepo;
            _prospectRepo = prospectRepo;
            _projectRepo = projectRepo;
            _propertyRepo = propertyRepo;
            _integrationAccRepo = integrationAccRepo;
            _facebookRepo = facebookRepo;
            _ivrAssignment = ivrAssignment;
            _deleteUsers = deleteUsers;
            _cityAssignments = cityAssignments;
            _zoneAssignments = zoneAssignments;
            _localityAssignments = localityAssignments;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<Response<UserDataCountDto>> Handle(DeleteUserRequest request, CancellationToken cancellationToken)
        {
            var user = (await _userRepository.ListAsync(new GetUserDetailsByIdSpec(request.UserId), cancellationToken)).FirstOrDefault();
            if (user == null)
            {
                throw new NotFoundException("No user found.");
            }
            var tenantId = _currentUser.GetTenant();
            var isAdmin = await _dapperRepository.IsAdminAsync(request.UserId, tenantId ?? string.Empty);
            if(isAdmin)
            {
                throw new ConflictException("Administrators Profile's cannot be delete");
            }
            var leadsCount = await _leadRepo.CountAsync(new GetAssignedLeadsByUserIdSpec(user.UserId), cancellationToken);
            var secLeadsCount = await _leadRepo.CountAsync(new GetSecAssignedLeadsByUserIdSpec(user.UserId), cancellationToken);
            var prospectCount = await _prospectRepo.CountAsync(new GetAssignedProspectByUserIdSpec(user.UserId), cancellationToken);
            var projectCount = await _projectRepo.CountAsync(new GetAssignedProjectByUserIdSpec(user.UserId), cancellationToken);
            var propertyCount = await _propertyRepo.CountAsync(new GetAssignedPropertyByUserIdSpec(user.UserId), cancellationToken);
            var integrationAccountInfo = await _integrationAccRepo.CountAsync(new GetAssignedIntegrationByUserIdSpec(user.UserId), cancellationToken);
            var facebookAccountInfo = await _facebookRepo.CountAsync(new GetAssignedFacebookIntegrationByUserIdSpec(user.UserId), cancellationToken);
            var ivrAssignment = await _ivrAssignment.CountAsync(new GetAssignedIVRAssignmentByUserIdSpec(user.UserId), cancellationToken);
            var cityAssignMent = await _cityAssignments.CountAsync(new GetAssignedCityUserIdSpec(user.UserId), cancellationToken);
            var zoneAssignMent = await _zoneAssignments.CountAsync(new GetAssignedZoneByUserIdSpec(user.UserId), cancellationToken);
            var localityAssignMent = await _localityAssignments.CountAsync(new GetAssignedLocationByUserIdSpec(user.UserId), cancellationToken);
            var detailsDto = new UserDataCountDto
            {
                Leads = leadsCount + secLeadsCount,
                Prospects = prospectCount,
                Projects = projectCount,
                Property = propertyCount,
                IntegrationAccountInfo = integrationAccountInfo,
                FacebookAccountInfo = facebookAccountInfo,
                IvrAssignment = ivrAssignment,
                CityAssignment = cityAssignMent,
                LocalityAssignment = localityAssignMent,
                ZoneAssignment = zoneAssignMent,
                IsDeleted = leadsCount + secLeadsCount == 0 && prospectCount == 0 && projectCount == 0 && propertyCount == 0 && integrationAccountInfo == 0 && facebookAccountInfo == 0
                && cityAssignMent==0 && localityAssignMent==0 && zoneAssignMent==0
            };

            if (!detailsDto.IsDeleted)
            {
                return new Response<UserDataCountDto>(detailsDto);
            }
            var deleteduser = await _userService.PermanentDeleteAsync(request.UserId.ToString(), cancellationToken);
            await _userRepository.SoftDeleteAsync(user, cancellationToken);

            var deleteduserDetails = deleteduser.Adapt<DeletedUser>();
            await _deleteUsers.AddAsync(deleteduserDetails);
            detailsDto.IsDeleted = true;
            return new Response<UserDataCountDto>(detailsDto);
        }
    }
}




