﻿using Lrb.Application.Identity.Users;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetDeleteUsersTrackerRequest : PaginationFilter, IRequest<PagedResponse<UserDeletedTrackerDto, string>>
    {
    }
    public class GetDeleteUserTrackerRequestHandler : IRequestHandler<GetDeleteUsersTrackerRequest, PagedResponse<UserDeletedTrackerDto, string>>
    {
        private readonly IReadRepository<UserDeletedTracker> _userTrackerRepo;
        public readonly IUserService _userService;
        public GetDeleteUserTrackerRequestHandler(IReadRepository<UserDeletedTracker> userTrackerRepo,
            IUserService userService)
        {
            _userTrackerRepo = userTrackerRepo;
            _userService = userService;
        }
        public async Task<PagedResponse<UserDeletedTrackerDto, string>> Handle(GetDeleteUsersTrackerRequest request, CancellationToken cancellationToken)
        {
            var userTracker = await _userTrackerRepo.ListAsync(new UserDeleteTrackerSpec(request), cancellationToken);
            var totalCount = await _userTrackerRepo.CountAsync(new GetUserDeleteTrackersCountSpec(), cancellationToken);
            var UserTrackerDto = userTracker.Adapt<List<UserDeletedTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(UserTrackerDto.Select(i => i.CreatedBy.ToString()).ToList(), cancellationToken);
            var deleteduser = await _userService.GetListOfUsersByIdsAsync(UserTrackerDto.Select(i => i.UserId.ToString()).ToList(), cancellationToken);

            foreach (var userTrackers in UserTrackerDto)
            {
                userTrackers.DeletedBy = users?.FirstOrDefault(i => userTrackers.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }

            return new(UserTrackerDto, totalCount);
        }
    }
}