﻿using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetImportUsersTrackerRequest : PaginationFilter, IRequest<PagedResponse<BulkUserUploadTrackerDto, string>>
    {
    }
    public class GetImportUsersTrackerRequestHandler : IRequestHandler<GetImportUsersTrackerRequest, PagedResponse<BulkUserUploadTrackerDto, string>>
    {
        private readonly IReadRepository<BulkUserUploadTracker> _trackerRepo;
        public GetImportUsersTrackerRequestHandler(IReadRepository<BulkUserUploadTracker> trackerRepo)

        {
            _trackerRepo = trackerRepo;
        }
        public async Task<PagedResponse<BulkUserUploadTrackerDto, string>> Handle(GetImportUsersTrackerRequest request, CancellationToken cancellationToken)
        {
            var userTracker = await _trackerRepo.ListAsync(new BulkUserUploadTrackerspec(request), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new BulkUserUploadTrackerspecCount(), cancellationToken);
            var userTrackers = userTracker.Adapt<List<BulkUserUploadTrackerDto>>(); 
            return new PagedResponse<BulkUserUploadTrackerDto, string>(userTrackers, totalCount);
        }

    }
}
