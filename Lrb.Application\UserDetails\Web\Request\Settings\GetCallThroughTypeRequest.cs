﻿using MediatR;

namespace Lrb.Application.UserDetails.Web
{
    public class GetCallThroughTypeRequest : IRequest<Response<UserChannelSettingsDto>>
    {
        public Guid UserId { get; set; }
    }
    public class GetCallThroughTypeRequestHandler : IRequestHandler<GetCallThroughTypeRequest, Response<UserChannelSettingsDto>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.UserDetails> _repo;
        public GetCallThroughTypeRequestHandler(IRepositoryWithEvents<Lrb.Domain.Entities.UserDetails> repo)
        {
            _repo = repo;
        }
        public async Task<Response<UserChannelSettingsDto>> Handle(GetCallThroughTypeRequest request, CancellationToken cancellationToken)
        {
            if (request.UserId == Guid.Empty)
            {
                throw new InvalidOperationException("Invalid user id");
            }
            UserChannelSettingsDto? channelSettings = new();
            var settings = (await _repo.FirstOrDefaultAsync(new GetUserDetailsByIdSpec(request.UserId), cancellationToken));
            channelSettings = settings.Adapt(channelSettings);
            return new(channelSettings);
        }
    }
}