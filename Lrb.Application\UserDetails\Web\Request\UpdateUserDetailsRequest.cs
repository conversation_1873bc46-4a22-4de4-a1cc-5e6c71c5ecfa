﻿using Lrb.Application.Attendance.Web.Specs;
using Lrb.Application.Identity.Roles;
using Lrb.Application.Identity.Users;
using Lrb.Shared.Authorization;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Security.Claims;
using Lrb.Shared.Extensions;
using System.Text;
using Lrb.Application.TimeZone.Dto;
using TimeZoneConverter;

namespace Lrb.Application.UserDetails.Web
{
    public class UpdateUserDetailsRequest : UpdateUserDto, IRequest<Response<Guid>>
    {

    }
    public class UpdateUserDetailsRequestHandler : IRequestHandler<UpdateUserDetailsRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsrepository;
        private readonly IRepositoryWithEvents<Department> _departmentRepository;
        private readonly IRepositoryWithEvents<Designation> _designationsrepository;
        private readonly IRepositoryWithEvents<UserDocument> _userDocumentRepository;
        private readonly IUserService _userService;
        private readonly IRoleService _roleService;
        private readonly IHttpContextAccessor _contextAccessor;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Domain.Entities.Attendance.AttendenceSettings> _attendancesettingsRepository;

        public UpdateUserDetailsRequestHandler(IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsrepository,
             IRepositoryWithEvents<Department> departmentRepository,
             IRepositoryWithEvents<Designation> designationsrepository,
             IRepositoryWithEvents<UserDocument> userDocumentRepository,
              IUserService userService,
              IRoleService roleService,
              IHttpContextAccessor contextAccessor,
              ICurrentUser currentUser,
              IRepositoryWithEvents<Domain.Entities.Attendance.AttendenceSettings> attendancesettingsRepository
            )
        {
            _userDetailsrepository = userDetailsrepository;
            _departmentRepository = departmentRepository;
            _designationsrepository = designationsrepository;
            _userDocumentRepository = userDocumentRepository;
            _userService = userService;
            _roleService = roleService;
            _contextAccessor = contextAccessor;
            _currentUser = currentUser;
            _attendancesettingsRepository = attendancesettingsRepository;
        }
        public async Task<Response<Guid>> Handle(UpdateUserDetailsRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.UserDetails? existingUser = null;
            var existingUsers = await _userDetailsrepository.ListAsync(new GetUserDetailsByIdSpec(request.UserId), cancellationToken);
            existingUser = existingUsers.FirstOrDefault();
            if (existingUser == null) { throw new NotFoundException("No user found by this id!"); }
            existingUser = request.Adapt(existingUser);
            existingUser.CurrentAddress = request.Address;
            if (request.ReportsTo == null || request.ReportsTo == Guid.Empty)
            {
                existingUser.ReportsTo = Guid.Empty;
            }
            else
            {
                var reportsTOUser = (await _userDetailsrepository.ListAsync(new GetUserDetailsByIdSpec(request.ReportsTo.Value), cancellationToken)).FirstOrDefault();
                if (reportsTOUser.ReportsTo != existingUser.UserId)
                {
                    existingUser.ReportsTo = request.ReportsTo.Value;
                }
                else
                {
                    throw new Exception("You can't report to this User because user is reporting to you");
                }
            }
            Department? department = null;
            department = await _departmentRepository.GetByIdAsync(request.DepartmentId, cancellationToken);
            if (department != null) { existingUser.Department = department; }
            Domain.Entities.Designation? designation = null;
            designation = await _designationsrepository.GetByIdAsync(request.DesignationId, cancellationToken) ?? null;
            if (designation != null) { existingUser.Designation = designation; }
            if (request.UserRoles?.Any() ?? false)
            {
                UserRolesRequest userRoleRequest = new();
                userRoleRequest.UserRoles = request.UserRoles;
                var result = await _userService.AssignRolesAsync(existingUser.UserId.ToString(), userRoleRequest, cancellationToken);
            }
            //await _userService.UpdateImageAsync(request.UserId.ToString(), request?.ImageUrl);
            UpdateUserRequest updateUserRequest = request.Adapt<UpdateUserRequest>();
            await _userService.UpdateAsync(updateUserRequest, request.UserId.ToString());
            #region TimeZone
            if (request.TimeZoneId != null)
            {
                CreateTimeZoneDto timeZoneInfoDto = new CreateTimeZoneDto();
                string? windowsZoneId = TZConvert.IanaToWindows(request.TimeZoneId ?? "Asia/Kolkata");
                var existingUserTimeZoneInfo = JsonConvert.DeserializeObject<CreateTimeZoneDto>(existingUser?.TimeZoneInfo ?? JsonConvert.SerializeObject(new CreateTimeZoneDto())) ?? new();
                timeZoneInfoDto.TimeZoneDisplay = request.TimeZone ?? existingUserTimeZoneInfo.TimeZoneDisplay;
                timeZoneInfoDto.TimeZoneName = windowsZoneId ?? existingUserTimeZoneInfo.TimeZoneName;
                timeZoneInfoDto.TimeZoneId = request.TimeZoneId ?? existingUserTimeZoneInfo.TimeZoneId;
                timeZoneInfoDto.BaseUTcOffset = request.BaseUTcOffset ?? existingUserTimeZoneInfo.BaseUTcOffset;
                var timeZoneInfo = JsonConvert.SerializeObject(timeZoneInfoDto);
                existingUser.TimeZoneInfo = timeZoneInfo ?? string.Empty;
            }
            else
            {
                existingUser.TimeZoneInfo = JsonConvert.SerializeObject(new CreateTimeZoneDto());
            }
            existingUser.ShouldShowTimeZone = request.ShouldShowTimeZone ?? existingUser.ShouldShowTimeZone;
            #endregion
            await _userDetailsrepository.UpdateAsync(existingUser, cancellationToken);
            var isInAdminRole = await _userService.IsInAdminRoleAsync(request.UserId.ToString() ?? string.Empty, cancellationToken);
            var tenantId = _currentUser.GetTenant();
            var existingAttendanceSettings = (await _attendancesettingsRepository.FirstOrDefaultAsync(new GetAttendanceSettingsSpec(), cancellationToken));


                if (isInAdminRole)
                {
                    var claim = new Claim(IdTokenClaimsKey.ShiftTime, "");
                    await _userService.UpdateUserShiftTimingClaimAsync(request.UserId.ToString(), claim, tenantId, cancellationToken);

                    if ((existingAttendanceSettings != null) && (existingAttendanceSettings.UserIds?.Contains(_currentUser.GetUserId()) ?? false))
                    {
                        existingAttendanceSettings.UserIds.Remove(request.UserId);
                        await _attendancesettingsRepository.UpdateAsync(existingAttendanceSettings, cancellationToken);
                    }
                }
                else if(!isInAdminRole)
                {
                    if (existingAttendanceSettings != null && (existingAttendanceSettings.IsEnabledForAllUsers))
                    {
                        var settings = new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            ContractResolver = new DefaultContractResolver
                            {
                                NamingStrategy = new CamelCaseNamingStrategy() // Optional: Convert property names to camel case
                            }
                        };
                        string jsonData = JsonConvert.SerializeObject(existingAttendanceSettings, settings);
                        byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonData);
                        byte[] compressedBytes = ObjectExensions.Compress(jsonBytes);
                        var result = Convert.ToBase64String(compressedBytes);
                        var claim = new Claim(IdTokenClaimsKey.ShiftTime, result);
                        await _userService.UpdateUserShiftTimingClaimAsync(request.UserId.ToString(), claim, tenantId, cancellationToken);
                    }

                }
            return new(existingUser.UserId);
        }
    }
}
