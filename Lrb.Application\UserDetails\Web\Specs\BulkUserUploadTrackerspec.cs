﻿using Lrb.Application.UserDetails.Web.Request;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.UserDetails.Web.Specs
{
    public class BulkUserUploadTrackerspec : EntitiesByPaginationFilterSpec<BulkUserUploadTracker>
    {
        public BulkUserUploadTrackerspec(GetImportUsersTrackerRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted).OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class BulkUserUploadTrackerspecCount : Specification<BulkUserUploadTracker>
    {
        public BulkUserUploadTrackerspecCount()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}
