﻿using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Team.Web.Requests;
using Lrb.Application.UserDetails.Web.Request;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.UserDetails.Web.Specs
{
    public class ExportUseTrackerSpec : EntitiesByPaginationFilterSpec<ExportUserTracker>
    {
        public ExportUseTrackerSpec(GetUserReportTrackerRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted).OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetExportUserTrackersCountSpec : Specification<ExportUserTracker>
    {
        public GetExportUserTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
    public class GetUserDeleteTrackersCountSpec : Specification<UserDeletedTracker>
    {
        public GetUserDeleteTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
    public class UserDeleteTrackerSpec : EntitiesByPaginationFilterSpec<UserDeletedTracker>
    {
        public UserDeleteTrackerSpec(GetDeleteUsersTrackerRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted).OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportTeamTrackersSpec : EntitiesByPaginationFilterSpec<ExportUserTracker>
    {
        public GetExportTeamTrackersSpec(TeamExportTrackerRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.Type == "exportteam")
                 .OrderByDescending(i => i.CreatedOn);
        }
    }
    public class GetExportTeamTrackersCountSpec : Specification<ExportUserTracker>
    {
        public GetExportTeamTrackersCountSpec()
        {
            Query.Where(i => !i.IsDeleted && i.Type == "exportteam");
        }
    }
}