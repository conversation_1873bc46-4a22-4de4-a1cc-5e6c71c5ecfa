﻿using System.Linq;

namespace Lrb.Application.UserDetails.Web
{
    public class GetUsersSpec : Specification<Domain.Entities.UserDetails>
    {
        public GetUsersSpec(GetAllUserDetailsRequest filter, List<Guid> userIds)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Department)
                .Include(i => i.Designation)
                .Include(i => i.UserDocuments)
                .OrderByDescending(i => i.LastModifiedOn);

            Query.Where(i => (userIds.Any() && userIds.Contains(i.UserId)) || (!string.IsNullOrWhiteSpace(filter.UserSearch) && (i.Department.Name.ToLower().Trim().Contains(filter.UserSearch) || i.Designation.Name.ToLower().Trim().Contains(filter.UserSearch) ||
            i.Description.ToLower().Trim().Contains(filter.UserSearch) || i.AltEmail.ToLower().Trim().Contains(filter.UserSearch) || i.AltPhoneNumber.Trim().Contains(filter.UserSearch))));
        }
        public GetUsersSpec(List<Guid> userIds)
        {
            Query.Where(i => !i.IsDeleted && userIds.Contains(i.UserId));
        }
        public GetUsersSpec()
        {
            Query.Where(i => !i.IsDeleted).OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetPaginatedUsersSpec : Specification<Domain.Entities.UserDetails>
    {
        public GetPaginatedUsersSpec(GetAllUserDetailsRequest filter, List<Guid> userIds)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Department)
                .Include(i => i.Designation)
                .Include(i => i.UserDocuments)
                .OrderByDescending(i => i.LastModifiedOn);

            Query.Where(i => (userIds.Any() && userIds.Contains(i.UserId)) || (!string.IsNullOrWhiteSpace(filter.UserSearch) && (i.Department.Name.ToLower().Trim().Contains(filter.UserSearch) || i.Designation.Name.ToLower().Trim().Contains(filter.UserSearch) ||
            i.Description.ToLower().Trim().Contains(filter.UserSearch) || i.AltEmail.ToLower().Trim().Contains(filter.UserSearch) || i.AltPhoneNumber.Trim().Contains(filter.UserSearch))));
            if (filter.UserIds != null)
            {
                Query.Where(i => filter.UserIds.Contains(i.UserId));
            }
            if (filter?.ReportsToIds?.Any() ?? false)
            {
                Query.Where(i => filter.ReportsToIds.Contains(i.ReportsTo));
            }
            if (filter?.Departments?.Any() ?? false)
            {
                filter.Departments = filter.Departments.ConvertAll(i => i.ToLower().Trim()).ToList();
                Query.Where(i => filter.Departments.Contains(i.Department.Name.ToLower().Trim()));
            }
            if (filter?.Designations?.Any() ?? false)
            {
                filter.Designations = filter.Designations.ConvertAll(i => i.ToLower().Trim()).ToList();
                Query.Where(i => filter.Designations.Contains(i.Designation.Name.ToLower().Trim()));
            }
        }
    }
}
