﻿using DocumentFormat.OpenXml;
using Lrb.Application.Reports.Web.Data.Dtos.Common;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Utils;
using Lrb.Shared.Extensions;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Data;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.Data.Utils
{
    public static class DataExcelHelper
    {
        public static class ExcelGeneration<T>
        {
            private static List<string> mergedCells = new();
            public static byte[] GenerateExcel(List<T> dtos, string reportName, FiltersDto? filtersDto, ExportTrackerDto exportTrackerDto, List<DataHeadersDto> dataHeaders,string timeZoneId, TimeSpan baseUtcOffset)
            {

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using MemoryStream memoryStream = new();
                using var package = new ExcelPackage(memoryStream);
                var ws = package.Workbook.Worksheets.Add("Sheet");
                var properties = typeof(T).GetProperties();
                var value = FormatHeaderRow(ws, properties, filtersDto, exportTrackerDto, reportName, dataHeaders,timeZoneId,baseUtcOffset);
                PopulateDataRows(ws, dtos, properties, filtersDto, exportTrackerDto, reportName, dataHeaders, value,timeZoneId,baseUtcOffset);
                CalculateColumnSum(ws, dtos, properties, filtersDto, exportTrackerDto, reportName, value, dataHeaders,timeZoneId,baseUtcOffset);
                ws.Cells[ws.Dimension.Address].AutoFitColumns();
                package.Save();
                return memoryStream.ToArray();
            }

            public static int FormatHeaderRow(ExcelWorksheet ws, PropertyInfo[] properties, FiltersDto? filters, ExportTrackerDto exportTrackerDto, string reportName, List<DataHeadersDto> dataHeaders, string timeZoneId, TimeSpan baseUtcOffset)
            {
                return V2FormatHeaderRowWithComplexProperties(ws, properties, filters, exportTrackerDto, reportName, dataHeaders,timeZoneId,baseUtcOffset);
            }
            private static int V2FormatHeaderRowWithComplexProperties(ExcelWorksheet ws, PropertyInfo[] properties, FiltersDto? filters, ExportTrackerDto exportTrackerDto, string reportName, List<DataHeadersDto> dataHeaders, string timeZoneId, TimeSpan baseUtcOffset)
            {
                mergedCells.Clear();
                var headerNames = new List<string>();
                var subHeaderNames = new List<string>();
                int headerRowCount = AddFilters(ws, filters, exportTrackerDto,timeZoneId,baseUtcOffset);
                AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);

                foreach (var item in dataHeaders)
                {
                    if (item.SubHeaderNames?.Any() ?? false)
                    {
                        mergedCells.Add($"{ColumnLetter(subHeaderNames.Count + 1)}${headerRowCount + 2}:{ColumnLetter(subHeaderNames.Count + item.SubHeaderNames.Count)}${headerRowCount + 2}");
                        if (!string.IsNullOrEmpty(item.Header))
                        {
                            subHeaderNames.Remove(item.Header);
                            headerNames.Add(item.Header);
                            headerNames.Add("");
                        }
                        foreach (var subProperty in item.SubHeaderNames)
                        {
                            if (!string.IsNullOrEmpty(subProperty))
                            {
                                subHeaderNames.Add(SplitCamelCase(subProperty));
                            }
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(item.Header))
                        {
                            subHeaderNames.Add(SplitCamelCase(item.Header));
                            headerNames.Add("");
                        }
                    }
                }

                foreach (var mergedCell in mergedCells)
                {
                    ws.Cells[mergedCell].Merge = true;
                }
                if (properties.Any(IsComplexProperty))
                {
                    string headerRange = $"A${headerRowCount + 2}:{ColumnLetter((subHeaderNames.Count))}${headerRowCount + 3}";
                    ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(subHeaderNames.Count)}${headerRowCount + 1}"].Merge = true;
                    ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray(), subHeaderNames.ToArray() });
                    var headerStyle = ws.Cells[headerRange].Style;
                    headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
                    headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                    headerStyle.Font.Size = 12;
                    headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
                    headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 3, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
                    ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                }
                else
                {
                    string headerRange = $"A${headerRowCount + 2}:{ColumnLetter((subHeaderNames.Count))}${headerRowCount + 2}";
                    ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(subHeaderNames.Count)}${headerRowCount + 1}"].Merge = true;
                    ws.Cells[headerRange].LoadFromArrays(new List<object[]> { subHeaderNames.ToArray() });
                    var headerStyle = ws.Cells[headerRange].Style;
                    headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
                    headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                    headerStyle.Font.Size = 12;
                    headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
                    headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 2, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
                    ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                }
                if (properties.Any(IsComplexProperty))
                {
                    return headerRowCount + 4;
                }
                else
                {
                    return headerRowCount + 3;
                }
            }
            private static void AddHeader(ExcelWorksheet ws, string cellAddress, string text, int fontSize, ExcelHorizontalAlignment alignment, bool isBold, int rowHeight)
            {
                var cell = ws.Cells[cellAddress];
                cell.Value = text;
                cell.Style.Font.Size = fontSize;
                cell.Style.Font.Bold = isBold;
                cell.Style.HorizontalAlignment = alignment;

                ws.Row(cell.Start.Row).Height = rowHeight;

                cell.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            }
            private static void ApplyCellBorderStyle(ExcelRangeBase range, ExcelBorderStyle borderStyle, System.Drawing.Color borderColor)
            {
                range.Style.Border.Left.Style = borderStyle;
                range.Style.Border.Left.Color.SetColor(borderColor);
                range.Style.Border.Top.Style = borderStyle;
                range.Style.Border.Top.Color.SetColor(borderColor);
                range.Style.Border.Bottom.Style = borderStyle;
                range.Style.Border.Bottom.Color.SetColor(borderColor);
                range.Style.Border.Right.Style = borderStyle;
                range.Style.Border.Right.Color.SetColor(borderColor);
            }
            private static void PopulateDataRows(ExcelWorksheet ws, List<T> data, PropertyInfo[] properties, FiltersDto? filters, ExportTrackerDto exportTrackerDto, string reportName, List<DataHeadersDto> dataHeaders, int? headerRowCount, string timeZoneId, TimeSpan baseUtcOffset)
            {
                int row = headerRowCount ?? 0;
                int id = 1;

                foreach (var item in data)
                {
                    int col = 1;

                    ws.Cells[row, col].Value = id;
                    var cell1 = ws.Cells[row, col];
                    cell1.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    ApplyCellBorderStyle(cell1, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                    id++;

                    foreach (var header in dataHeaders)
                    {
                        if (!string.IsNullOrEmpty(header.Header))
                        {
                            if (header.SubHeaderNames?.Any() ?? false)
                            {
                                PropertyInfo? dataProperty = item?.GetType()?.GetProperty("Data");
                                if (header.Header != "SlNo")
                                {
                                    foreach (var subHeader in header.SubHeaderNames)
                                    {
                                        var dataValue = dataProperty?.GetValue(item);
                                        if (dataProperty != null && dataValue != null)
                                        {
                                            var matchingItem = FindItemByPropertyValue(dataValue, "Status", subHeader);
                                            var newValue = GetCountFromDynamicObject(matchingItem);
                                            var cell = ws.Cells[row, col];
                                            cell.Value = newValue.ToString();
                                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                            ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                            col++;
                                        }
                                        else
                                        {
                                            var cell = ws.Cells[row, col];
                                            cell.Value = "0";
                                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                            ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                            col++;
                                        }
                                    }
                                }
                                else
                                {
                                    col++;
                                }
                            }
                            else
                            {
                                var property = item?.GetType().GetProperty(header.Header);
                                var value = property?.GetValue(item);
                                if (value != null)
                                {
                                    var cell = ws.Cells[row, col];
                                    cell.Value = value.ToString();
                                    cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                    ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                    col++;
                                }
                                else
                                {
                                    PropertyInfo? dataProperty = item?.GetType()?.GetProperty("Data");
                                    if (header.Header != "SlNo")
                                    {
                                        var dataValue = dataProperty?.GetValue(item);
                                        if (dataProperty != null && dataValue != null)
                                        {
                                            var matchingItem = FindItemByPropertyValue(dataValue, "Status", header.Header);
                                            var newValue = GetCountFromDynamicObject(matchingItem);
                                            var cell = ws.Cells[row, col];
                                            cell.Value = newValue.ToString();
                                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                            ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                            col++;
                                        }
                                        else
                                        {
                                            var cell = ws.Cells[row, col];
                                            cell.Value = "0";
                                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                            ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                            col++;
                                        }
                                    }
                                    else
                                    {
                                        col++;
                                    }
                                }
                            }
                        }
                    }
                    row++;
                }
            }
            static object? FindItemByPropertyValue(object collection, string propertyName, string propertyValue)
            {
                var result = ((IEnumerable<object>)collection)
                    ?.Where(item => GetPropertyValue<string>(item, propertyName) == propertyValue)?.ToList();
                return result?.FirstOrDefault() ?? default;
            }
            static long GetCountFromDynamicObject(object? dynamicObject)
            {
                PropertyInfo? countProperty = dynamicObject?.GetType().GetProperty("Count");
                if (countProperty != null)
                {
                    object? countValue = countProperty.GetValue(dynamicObject);
                    if (countValue != null && countValue is long)
                    {
                        return (long)countValue;
                    }
                }
                return 0;
            }
            static T GetPropertyValue<T>(object obj, string propertyName)
            {
                try
                {
                    Type type = obj.GetType();
                    PropertyInfo? property = type.GetProperty(propertyName);

                    if (property != null)
                    {
                        object value = property.GetValue(obj);
                        if (value is T)
                        {
                            return (T)value;
                        }
                    }
                }
                catch (Exception ex) { }
                return default;
            }
            private static string ColumnLetter(int columnNumber)
            {
                int dividend = columnNumber;
                string columnLetter = String.Empty;

                while (dividend > 0)
                {
                    int modulo = (dividend - 1) % 26;
                    columnLetter = Convert.ToChar('A' + modulo) + columnLetter;
                    dividend = (dividend - modulo) / 26;
                }

                return columnLetter;
            }
            private static string SplitCamelCase(string input)
            {
                return Regex.Replace(input, "(?<=[a-z])([A-Z])", " $1", RegexOptions.Compiled).Trim();
            }
            private static bool IsSimpleProperty(PropertyInfo property)
            {
                Type propertyType = property.PropertyType;

                return propertyType.IsPrimitive || propertyType == typeof(string) || propertyType == typeof(TimeOnly?);

            }
            private static bool IsComplexProperty(PropertyInfo property)
            {
                Type propertyType = property.PropertyType;

                return !IsSimpleProperty(property) && propertyType.IsClass && propertyType != typeof(string);
            }
            private static void CalculateColumnSum(ExcelWorksheet ws, List<T> data, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, int? headerRowCount, List<DataHeadersDto> dataHeaders,string timeZoneId, TimeSpan baseUtcOffset)
            {
                int row = data.Count + headerRowCount ?? 0;

                var totalRow = ws.Cells[row, ws.Dimension.Start.Column, row, ws.Dimension.End.Column];
                totalRow.Style.Fill.PatternType = ExcelFillStyle.Solid;
                totalRow.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Yellow);
                ApplyCellBorderStyle(totalRow, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                foreach (var header in dataHeaders)
                {
                    if (!string.IsNullOrEmpty(header.Header))
                    {
                        if (header.SubHeaderNames?.Any() ?? false)
                        {
                            foreach (var subHeader in header.SubHeaderNames)
                            {
                                long? sum = 0;
                                foreach (var item in data)
                                {
                                    PropertyInfo? dataProperty = item?.GetType()?.GetProperty("Data");
                                    var dataValue = dataProperty?.GetValue(item);
                                    if (dataProperty != null && dataValue != null)
                                    {
                                        var matchingItem = FindItemByPropertyValue(dataValue, "Status", subHeader);
                                        var newValue = GetCountFromDynamicObject(matchingItem);
                                        if (long.TryParse(newValue.ToString(), out long val))
                                        {
                                            sum += val;
                                        }
                                    }
                                }
                                ws.Cells[row, GetColumnIndex(ws, subHeader, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Value = sum;
                                ws.Cells[row, GetColumnIndex(ws, subHeader, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            }
                        }
                        else
                        {
                            long? sum = 0;
                            foreach (var item in data)
                            {
                                var property = item?.GetType().GetProperty(header.Header);
                                var value = property?.GetValue(item);
                                if (value != null)
                                {
                                    if (long.TryParse(value.ToString(), out long val))
                                    {
                                        sum += val;
                                    }
                                    else
                                    {
                                        sum = null;
                                    }
                                }
                                else
                                {
                                    PropertyInfo? dataProperty = item?.GetType()?.GetProperty("Data");
                                    var dataValue = dataProperty?.GetValue(item);
                                    if (dataProperty != null && dataValue != null)
                                    {
                                        var matchingItem = FindItemByPropertyValue(dataValue, "Status", header.Header);
                                        var newValue = GetCountFromDynamicObject(matchingItem);
                                        sum += newValue;
                                    }
                                }
                            }
                            ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto,timeZoneId,baseUtcOffset)].Value = "TOTAL";
                            ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto,timeZoneId,baseUtcOffset)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            var totalCell = ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)];
                            totalCell.Style.Font.Bold = true;
                            ws.Cells[row, GetColumnIndex(ws, header.Header, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Value = sum;
                            ws.Cells[row, GetColumnIndex(ws, header.Header, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
                    }
                }


                //foreach (var property in properties)
                //{
                //    var propertyType = property.PropertyType;

                //    if (IsSimpleProperty(property) && (propertyType == typeof(long) || (propertyType == typeof(string))))
                //    {
                //        long sum = 0;
                //        foreach (var item in data)
                //        {
                //            var value = property.GetValue(item) as long?;
                //            if (value != null)
                //            {
                //                sum += (long)value;
                //            }
                //        }
                //        ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto)].Value = "TOTAL";
                //        ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                //        var totalCell = ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto)];
                //        totalCell.Style.Font.Bold = true;
                //        if (propertyType == typeof(long))
                //        {
                //            ws.Cells[row, GetColumnIndex(ws, property.Name, properties, filters, exportTrackerDto)].Value = sum;
                //        }
                //        ws.Cells[row, GetColumnIndex(ws, property.Name, properties, filters, exportTrackerDto)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                //    }

                //    else if (IsComplexProperty(property))
                //    {
                //        var subProperties = propertyType.GetProperties();

                //        foreach (var subProperty in subProperties)
                //        {
                //            if (IsSimpleProperty(subProperty) && subProperty.PropertyType == typeof(long))
                //            {
                //                long sum = 0;

                //                foreach (var item in data)
                //                {
                //                    var complexValue = property.GetValue(item);
                //                    if (complexValue != null)
                //                    {
                //                        var subValue = subProperty.GetValue(complexValue);
                //                        if (subValue != null)
                //                        {
                //                            sum += (long)subValue;
                //                        }
                //                    }
                //                }
                //                int counter = 0;
                //                var columnIndex = GetColumnIndex(ws, subProperty.Name, properties, filters, exportTrackerDto, counter);
                //                while (ws.Cells[row, columnIndex].Value != null)
                //                {
                //                    counter++;
                //                    columnIndex = GetColumnIndex(ws, subProperty.Name, properties, filters, exportTrackerDto, counter);
                //                }
                //                ws.Cells[row, columnIndex].Value = sum;
                //                ws.Cells[row, columnIndex].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                //            }
                //        }
                //    }
                //}

                ws.Cells[row + 1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }
            private static int GetColumnIndex(ExcelWorksheet ws, string columnName, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string timeZoneId, TimeSpan baseUtcOffset ,int? counter = null)
            {
                int headerRowCount = AddFilters(ws, filters, exportTrackerDto,timeZoneId,baseUtcOffset);
                bool hasComplexProperty = properties.Any(IsComplexProperty);
                int headerRow = hasComplexProperty ? headerRowCount + 3 : headerRowCount + 2;
                counter ??= 0;
                for (int col = 1; col <= ws.Dimension.Columns; col++)
                {
                    string columnHeader = ws.Cells[headerRow, col].Text.Trim();

                    if (columnHeader == SplitCamelCase(columnName) && counter == 0)
                    {
                        return col;
                    }
                    else if (columnHeader == SplitCamelCase(columnName))
                    {
                        counter--;
                    }
                }
                return 3;
            }
            private static int AddFilters(ExcelWorksheet ws, FiltersDto? filters, ExportTrackerDto exportTrackerDto, string timeZoneId, TimeSpan baseUtcOffset)
            {
                int startRow = AddExportTracker(ws, exportTrackerDto, 1);
                var filtersRow = startRow + 1;


                var properties = typeof(FiltersDto).GetProperties();

                for (int i = 0; i < properties.Length; i++)
                {
                    var property = properties[i];
                    var value = property.GetValue(filters);

                    ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";
                    if (property.Name == "FromDate" && value is DateTime fromDate)
                    {
                        ws.Cells[filtersRow + i, 2].Value = fromDate.ToParticularTimeZone(timeZoneId, baseUtcOffset).ToString("dd-MM-yyyy");
                    }
                    else if (property.Name == "ToDate" && value is DateTime todate)
                    {
                        ws.Cells[filtersRow + i, 2].Value = todate.ToParticularTimeZone(timeZoneId, baseUtcOffset).ToString("dd-MM-yyyy");
                    }
                    else if (value != null)
                    {
                        ws.Cells[filtersRow + i, 2].Value = value.ToString();
                    }
                    else
                    {
                        ws.Cells[filtersRow + i, 2].Value = "N/A";
                    }
                }

                var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
                var filterStyle = filterRange.Style;
                filterStyle.Font.Bold = true;
                filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
                filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                return properties.Length + startRow;
            }
            private static int AddExportTracker(ExcelWorksheet ws, ExportTrackerDto exportTrackerDto, int startRow)
            {
                var filtersRow = startRow;
                var properties = typeof(ExportTrackerDto).GetProperties();

                for (int i = 0; i < properties.Length; i++)
                {
                    var property = properties[i];
                    var value = property.GetValue(exportTrackerDto);
                    ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";

                    if (property.Name == "Type" && value != null)
                    {
                        ws.Cells[filtersRow + i, 2].Value = GetReportType(value.ToString());
                    }
                    else if (property.Name == "CreatedOn" && value is DateTime createdOn)
                    {
                        ws.Cells[filtersRow + i, 2].Value = createdOn.ToString("dd-MM-yyyy");
                    }
                    else if (value != null)
                    {
                        ws.Cells[filtersRow + i, 2].Value = value.ToString();
                    }
                    else
                    {
                        ws.Cells[filtersRow + i, 2].Value = "N/A";
                    }
                }

                var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
                var filterStyle = filterRange.Style;
                filterStyle.Font.Bold = true;
                filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
                filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                return properties.Length;
            }
            private static string GetReportType(string name)
            {
                var reportTypes = new Dictionary<string, string>()
            {
                {"statusreportbysubsource", "Status report by Sub Source" },
                {"statusreportbyproject","Status Report By Project" },
                {"meetingandvisitreport","Meeting And Visit Report"},
                {"statusreportbysource","Status Report By source" },
                {"statusreportbyuser","Status Report By User" },
                {"statusreportbyagency","Status Report By Agency" },
                {"substatusreportbyuser","Sub Status Report By User" },
                {"substatusreportbysubsource","Sub Status Report By Subsource"},
                {"useractivityreport","User Activity Report" },
                {"userdataactivityreport","User Data Activity Report" },
                {"projectreportbysubstatus","Project Report By Substatus" },
                {"exportproperties","Export Properties" },
                {"calllogreportbyuser","Call Log Report" },
                {"datastatusreportbyuser","Data Status Report By User" },
                {"datasubsourcereportbystatus","Data Status Report By Sub Source" },
                {"datasourcereportbystatus","Data Status Report By Source" },
                {"dataprojectreportbystatus","Data Status Report By Project" },
                {"exportdatacalllogreportbyuser","Data Call Log Report" },
                {"uservssource","User Vs Source Reports" },
                {"uservssubsource","User Vs Sub Source Reports" }
            };
                return reportTypes[name];
            }
            public static byte[] GenerateExcel(List<T> dtos, string reportName, FiltersDto? filtersDto, ExportTrackerDto exportTrackerDto, string timeZoneId, TimeSpan baseUtcOffset)
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using MemoryStream memoryStream = new();
                using var package = new ExcelPackage(memoryStream);
                var ws = package.Workbook.Worksheets.Add("Sheet");
                var properties = typeof(T).GetProperties();
                var value = FormatHeaderRow(ws, properties, filtersDto, exportTrackerDto, reportName,timeZoneId,baseUtcOffset);
                PopulateDataRows(ws, dtos, properties, filtersDto, exportTrackerDto, reportName,timeZoneId,baseUtcOffset);
                CalculateColumnSum(ws, dtos, properties, filtersDto, exportTrackerDto, reportName,timeZoneId,baseUtcOffset);
                ws.Cells[ws.Dimension.Address].AutoFitColumns();
                package.Save();
                return memoryStream.ToArray();
            }



            public static int FormatHeaderRow(ExcelWorksheet ws, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, string timeZoneId, TimeSpan baseUtcOffset)
            {
                bool hasComplexProperty = properties.Any(IsComplexProperty);

                if (hasComplexProperty)
                {
                    return FormatHeaderRowWithComplexProperties(ws, properties, filters, exportTrackerDto, reportName,timeZoneId,baseUtcOffset);
                }
                else
                {
                    return FormatHeaderRowWithSimpleProperties(ws, properties, filters, exportTrackerDto, reportName,timeZoneId,baseUtcOffset);
                }
            }
            private static int FormatHeaderRowWithComplexProperties(ExcelWorksheet ws, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, string timeZoneId, TimeSpan baseUtcOffset)
            {
                mergedCells.Clear();
                var headerNames = new List<string>();
                var subHeaderNames = new List<string>();
                int headerRowCount = AddFilters(ws, filters, exportTrackerDto,timeZoneId,baseUtcOffset);
                AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);

                foreach (var property in properties)
                {

                    if (IsSimpleProperty(property))
                    {
                        if (property.Name != "Active")
                        {
                            headerNames.Add("");
                        }
                        if (property.Name == "All")
                        {
                            subHeaderNames.Add("All (Active)");
                        }
                        else if (property.Name == "Active")
                        {
                            subHeaderNames.Remove("Active");
                        }
                        else
                        {
                            subHeaderNames.Add(SplitCamelCase(property.Name));
                        }
                    }
                    else if (IsComplexProperty(property))
                    {
                        var subProperties = property.PropertyType.GetProperties();
                        int subPropertyCount = CountSubProperties(property);
                        mergedCells.Add($"{ColumnLetter(headerNames.Count + 1)}${headerRowCount + 2}:{ColumnLetter(headerNames.Count + subPropertyCount)}${headerRowCount + 2}");

                        for (int i = 0; i < subPropertyCount; i++)
                        {
                            headerNames.Add(SplitCamelCase(property.Name));
                        }

                        foreach (var subProperty in subProperties)
                        {
                            if (IsSimpleProperty(subProperty))
                            {
                                subHeaderNames.Add(SplitCamelCase(subProperty.Name));
                            }
                        }
                    }
                }

                foreach (var mergedCell in mergedCells)
                {
                    ws.Cells[mergedCell].Merge = true;
                }

                string headerRange = $"A${headerRowCount + 2}:{ColumnLetter(headerNames.Count)}${headerRowCount + 3}";
                ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(headerNames.Count)}${headerRowCount + 1}"].Merge = true;
                ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray(), subHeaderNames.ToArray() });

                var headerStyle = ws.Cells[headerRange].Style;
                headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
                headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                headerStyle.Font.Size = 12;
                headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
                headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 3, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
                ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                return headerRowCount + 4;
            }

            private static int FormatHeaderRowWithSimpleProperties(ExcelWorksheet ws, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, string timeZoneId, TimeSpan baseUtcOffset)
            {
                var headerNames = new List<string>();
                int headerRowCount = AddFilters(ws, filters, exportTrackerDto,timeZoneId,baseUtcOffset);

                AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);

                foreach (var property in properties)
                {

                    headerNames.Add(SplitCamelCase(property.Name));


                }
                string headerRange = $"A${headerRowCount + 2}:{ColumnLetter(headerNames.Count)}{headerRowCount + 2}";

                ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(headerNames.Count)}${headerRowCount + 1}"].Merge = true;

                ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray() });

                var headerStyle = ws.Cells[headerRange].Style;
                headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
                headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                headerStyle.Font.Size = 12;
                headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
                headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 2, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
                ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                return headerRowCount + 3;

            }
            private static void PopulateDataRows(ExcelWorksheet ws, List<T> dtos, PropertyInfo[] properties, FiltersDto filtersDto, ExportTrackerDto exportTrackerDto, string reportName, int value)
            {
                throw new NotImplementedException();
            }

            private static void PopulateDataRows(ExcelWorksheet ws, List<T> data, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, string timeZoneId, TimeSpan baseUtcOffset)
            {
                int headerRowCount = FormatHeaderRow(ws, properties, filters, exportTrackerDto, reportName,timeZoneId,baseUtcOffset);
                int row = headerRowCount;
                int id = 1;

                foreach (var item in data)
                {
                    int col = 1;

                    ws.Cells[row, col].Value = id;
                    var cell1 = ws.Cells[row, col];
                    cell1.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    ApplyCellBorderStyle(cell1, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                    id++;

                    foreach (var property in properties)
                    {
                        var value = property.GetValue(item);

                        if (value != null)
                        {
                            if (IsComplexProperty(property))
                            {
                                if (property.Name == "Incoming" || property.Name == "Outgoing")
                                {
                                    PopulateComplexProperty(ws, row, col, value, property.Name);
                                    col += CountSubProperties(property);
                                }
                                else
                                {
                                    var cell = ws.Cells[row, col];
                                    cell.Value = property.Name;
                                    cell.Style.Font.Bold = true;
                                    cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                    ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                                    col++;
                                }
                            }
                            else
                            {
                                var cell = ws.Cells[row, col];

                                cell.Value = value.ToString();

                                cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                                ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                col++;
                            }
                        }
                        else
                        {
                            col++;
                        }
                    }

                    row++;
                }
            }
            private static void PopulateComplexProperty(ExcelWorksheet ws, int row, int col, object value, string propertyName)
            {
                var subProperties = value.GetType().GetProperties();


                foreach (var subProperty in subProperties)
                {
                    var subValue = subProperty.GetValue(value);
                    var cell = ws.Cells[row, col];
                    cell.Value = subValue;
                    cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);


                    var cellStyle = cell.Style;
                    cellStyle.Fill.PatternType = ExcelFillStyle.Solid;


                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(255, 255, 255));


                    col++;
                }
            }

            private static int CountSubProperties(PropertyInfo property)
            {
                var subProperties = property.PropertyType.GetProperties();
                return subProperties.Length;
            }

            private static void PopulateSubProperties(ExcelWorksheet ws, int row, int col, object value)
            {
                var subProperties = value.GetType().GetProperties();

                foreach (var subProperty in subProperties)
                {
                    var subValue = subProperty.GetValue(value);
                    ws.Cells[row, col].Value = subValue;
                    col++;
                }
            }
            private static void CalculateColumnSum(ExcelWorksheet ws, List<T> data, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, string timeZoneId, TimeSpan baseUtcOffset)
            {
                int headerRowCount = FormatHeaderRow(ws, properties, filters, exportTrackerDto, reportName,timeZoneId,baseUtcOffset);
                int row = data.Count + headerRowCount;


                var totalRow = ws.Cells[row, ws.Dimension.Start.Column, row, ws.Dimension.End.Column];
                totalRow.Style.Fill.PatternType = ExcelFillStyle.Solid;
                totalRow.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Yellow);
                ApplyCellBorderStyle(totalRow, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                foreach (var property in properties)
                {
                    var propertyType = property.PropertyType;

                    if (IsSimpleProperty(property) && (propertyType == typeof(long) || (propertyType == typeof(string) && property.Name.Contains("TalkTime"))))
                    {
                        List<TimeSpan> timeSpans = new();
                        long sum = 0;
                        foreach (var item in data)
                        {
                            if (propertyType == typeof(string) && (property.GetValue(item)?.ToString()?.IsTimeSpan() ?? false))
                            {
                                var value = property.GetValue(item) as string;
                                if (value != null)
                                {
                                    timeSpans.Add(TimeSpan.Parse(value));
                                }
                            }
                            else
                            {
                                var value = property.GetValue(item) as long?;

                                if (value != null)
                                {
                                    sum += (long)value;
                                }
                            }
                        }


                        ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Value = "TOTAL";
                        ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        var totalCell = ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)];
                        totalCell.Style.Font.Bold = true;
                        if (propertyType == typeof(long))
                        {
                            ws.Cells[row, GetColumnIndex(ws, property.Name, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Value = sum;
                        }
                        else if (timeSpans.Any())
                        {
                            TimeSpan totalTime = timeSpans.Aggregate(TimeSpan.Zero, (acc, time) => acc + time);
                            ws.Cells[row, GetColumnIndex(ws, property.Name, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Value = totalTime.ToString(@"hh\:mm\:ss");
                        }
                        ws.Cells[row, GetColumnIndex(ws, property.Name, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }

                    else if (IsComplexProperty(property))
                    {
                        var subProperties = propertyType.GetProperties();

                        foreach (var subProperty in subProperties)
                        {
                            if (IsSimpleProperty(subProperty) && subProperty.PropertyType == typeof(long))
                            {
                                long sum = 0;

                                foreach (var item in data)
                                {
                                    var complexValue = property.GetValue(item);
                                    if (complexValue != null)
                                    {
                                        var subValue = subProperty.GetValue(complexValue);
                                        if (subValue != null)
                                        {
                                            sum += (long)subValue;
                                        }
                                    }
                                }
                                int counter = 0;
                                var columnIndex = GetColumnIndex(ws, subProperty.Name, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset, counter);
                                while (ws.Cells[row, columnIndex].Value != null)
                                {
                                    counter++;
                                    columnIndex = GetColumnIndex(ws, subProperty.Name, properties, filters, exportTrackerDto, timeZoneId, baseUtcOffset, counter);
                                }
                                ws.Cells[row, columnIndex].Value = sum;
                                ws.Cells[row, columnIndex].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            }
                        }
                    }
                }

                ws.Cells[row + 1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            }
        }
    }
}






