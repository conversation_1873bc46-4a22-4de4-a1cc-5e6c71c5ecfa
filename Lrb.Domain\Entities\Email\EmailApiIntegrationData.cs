﻿namespace Lrb.Domain.Entities
{
    public class EmailApiIntegrationData : AuditableEntity, IAggregateRoot
    {
        public EmailServiceProviders ServiceProviders { get; set; }
        public string? ApiKey { get; set; }
        public string? AuthToken { get; set; }
        public bool? IsPrimary { get; set; }
        public string? DefaultScope { get; set; }
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
        public string? AuthorityUri { get; set; }

    }
}
