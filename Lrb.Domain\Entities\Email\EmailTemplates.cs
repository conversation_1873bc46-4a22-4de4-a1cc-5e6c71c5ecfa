﻿namespace Lrb.Domain.Entities
{
    public class EmailTemplates : AuditableEntity, IAggregateRoot
    {
        public Event? Event { get; set; }
        public string? Name { get; set; }
        public string? Subject { get; set; }
        public string? Body { get; set; }
        public TemplateType? TemplateType { get; set; }
        public EmailBodyType? BodyType { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; }
        public bool IsPrimary { get; set; }
        public List<string>? AttachmentUrls { get; set; }
        public bool IsLeadSpecific { get; set; }
    }
}
