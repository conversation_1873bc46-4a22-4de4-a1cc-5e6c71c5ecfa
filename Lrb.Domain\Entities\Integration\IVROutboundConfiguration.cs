﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class IVROutboundConfiguration : AuditableEntity, IAggregateRoot
    {
        public string? MethodType { get; set; }
        public string? BaseURL { get; set; }
        public string? Resources { get; set; }
        
        [Column(TypeName = "jsonb")]
        public IDictionary<string, string>? QueryParameters { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<string, string>? HeaderVariables { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<string, string>? BodyVariables { get; set; }
        public bool IsPrimary { get; set; }
        public bool IsVirtualNumberRequired { get; set; }
        public string? ContentType { get; set; }
        public bool IsSpecialChartacterDisabled { get; set; }
        //Length of agent and customer number to be send on C2C api
        public int? C2CDigitsLimit {  get; set; }
        public string? CurlForAuthentication {  get; set; }
        public string? ResponseParameterKey { get; set; }
    }
}
