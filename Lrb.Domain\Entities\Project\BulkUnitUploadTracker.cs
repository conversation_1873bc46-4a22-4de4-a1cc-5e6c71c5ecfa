﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class BulkUnitUploadTracker : AuditableEntity, IAggregateRoot
    {
        public int TotalCount { get; set; }
        public int DistinctUnitCount { get; set; }
        public int TotalUploadedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int UnitCount { get; set; }
        public int InvalidCount { get; set; }
        public string? S3BucketKey { get; set; }
        public string? InvalidDataS3BucketKey { get; set; }
        public string? SheetName { get; set; }

        [Column(TypeName = "jsonb")]
        public Dictionary<UnitDataColumn, string>? MappedColumnData { get; set; }
        public UploadStatus Status { get; set; }
        public string? Message { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? UserIds { get; set; }
        [Column(TypeName = "jsonb")]
        public List<Guid>? ProjectIds { get; set; }
    }
}
